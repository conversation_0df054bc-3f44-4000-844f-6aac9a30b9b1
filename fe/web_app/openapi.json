{"openapi": "3.0.3", "info": {"title": "API", "version": "1.0.0", "contact": {}}, "paths": {"/cashshop": {"get": {"operationId": "get~render.notice_id", "summary": "Notice Id", "tags": ["render"], "parameters": [{"name": "nid", "schema": {"type": "string"}, "required": true, "in": "path"}], "responses": {"default": {"description": "OK"}}}}, "/notice": {"get": {"operationId": "get~render.notice_id", "summary": "Notice Id", "tags": ["render"], "parameters": [{"name": "nid", "schema": {"type": "string"}, "required": true, "in": "path"}], "responses": {"default": {"description": "OK"}}}}, "/ranking": {"get": {"operationId": "get~render.notice_id", "summary": "Notice Id", "tags": ["render"], "parameters": [{"name": "nid", "schema": {"type": "string"}, "required": true, "in": "path"}], "responses": {"default": {"description": "OK"}}}}, "/wz": {"get": {"operationId": "get~render.notice_id", "summary": "Notice Id", "tags": ["render"], "parameters": [{"name": "nid", "schema": {"type": "string"}, "required": true, "in": "path"}], "responses": {"default": {"description": "OK"}}}}, "/": {"get": {"operationId": "get~render.notice_id", "summary": "Notice Id", "tags": ["render"], "parameters": [{"name": "nid", "schema": {"type": "string"}, "required": true, "in": "path"}], "responses": {"default": {"description": "OK"}}}}, "/forgot": {"get": {"operationId": "get~render.notice_id", "summary": "Notice Id", "tags": ["render"], "parameters": [{"name": "nid", "schema": {"type": "string"}, "required": true, "in": "path"}], "responses": {"default": {"description": "OK"}}}}, "/download": {"get": {"operationId": "get~render.notice_id", "summary": "Notice Id", "tags": ["render"], "parameters": [{"name": "nid", "schema": {"type": "string"}, "required": true, "in": "path"}], "responses": {"default": {"description": "OK"}}}}, "/api/v1/auth/reset": {"put": {"operationId": "put~v1.ResetPassword", "summary": "找回密码-提交确认", "tags": ["v1"], "responses": {"default": {"content": {"application/json": {"schema": {"properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"title": "Data", "type": "null"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[NoneType]", "type": "object"}}}, "description": "Default Response"}}, "requestBody": {"content": {"application/json": {"schema": {"properties": {"username": {"title": "Username", "type": "string"}, "password": {"pattern": "[\\da-zA-Z!@#$%^&*,./|}{()_+=\\-]{6,16}$", "title": "Password", "type": "string"}, "captcha": {"title": "<PERSON><PERSON>", "type": "string"}}, "required": ["username", "password", "<PERSON><PERSON>a"], "title": "ResetPasswordRequest", "type": "object"}}}}}, "post": {"operationId": "post~v1.ResetPassword", "summary": "找回密码-获取验证码", "tags": ["v1"], "responses": {"default": {"content": {"application/json": {"schema": {"properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"title": "Data", "type": "null"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[NoneType]", "type": "object"}}}, "description": "Default Response"}}, "requestBody": {"content": {"application/json": {"schema": {"properties": {"username": {"title": "Username", "type": "string"}}, "required": ["username"], "title": "ResetPwdCaptchaRequest", "type": "object"}}}}}}, "/api/v1/character/list": {"get": {"operationId": "get~v1.Char<PERSON>ist<PERSON>iew", "summary": "获取用户角色列表", "tags": ["v1"], "responses": {"default": {"content": {"application/json": {"schema": {"$defs": {"CharInfo": {"properties": {"id": {"title": "Id", "type": "integer"}, "accountid": {"title": "Accountid", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "level": {"title": "Level", "type": "integer"}, "str": {"title": "Str", "type": "integer"}, "dex": {"title": "<PERSON>", "type": "integer"}, "int": {"title": "Int", "type": "integer"}, "luk": {"title": "Luk", "type": "integer"}, "maxhp": {"title": "<PERSON><PERSON>", "type": "integer"}, "maxmp": {"title": "<PERSON><PERSON>", "type": "integer"}, "gender": {"title": "Gender", "type": "integer"}, "map": {"title": "Map", "type": "integer"}, "rank": {"title": "Rank", "type": "integer"}, "meso": {"title": "Meso", "type": "integer"}, "job": {"title": "Job", "type": "integer"}, "fame": {"title": "Fame", "type": "integer"}, "hair": {"title": "Hair", "type": "integer"}, "face": {"title": "Face", "type": "integer"}, "skincolor": {"title": "Skincolor", "type": "integer"}, "guildid": {"title": "<PERSON><PERSON>", "type": "integer"}, "createdate": {"format": "date-time", "title": "Createdate", "type": "string"}, "lastLogoutTime": {"format": "date-time", "title": "Lastlogouttime", "type": "string"}, "jailexpire": {"title": "Jailexpire", "type": "integer"}}, "required": ["id", "accountid", "name", "level", "str", "dex", "int", "luk", "maxhp", "maxmp", "gender", "map", "rank", "meso", "job", "fame", "hair", "face", "skincolor", "guildid", "createdate", "lastLogoutTime", "jailexpire"], "title": "CharInfo", "type": "object"}, "CharListResponse": {"properties": {"items": {"items": {"$ref": "#/components/schemas/CharInfo"}, "title": "Items", "type": "array"}}, "required": ["items"], "title": "CharListResponse", "type": "object"}}, "properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"$ref": "#/components/schemas/CharListResponse"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[CharListResponse]", "type": "object"}}}, "description": "Default Response"}}}}, "/api/v1/cashshop/gift": {"post": {"operationId": "post~v1.CSItemGiftView", "summary": "赠送商城物品", "tags": ["v1"], "responses": {"default": {"content": {"application/json": {"schema": {"properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"title": "Data", "type": "null"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[NoneType]", "type": "object"}}}, "description": "Default Response"}}, "requestBody": {"content": {"application/json": {"schema": {"properties": {"shop_id": {"title": "Shop Id", "type": "integer"}, "accept": {"title": "Accept", "type": "string"}, "birthday": {"title": "Birthday", "type": "string"}}, "required": ["shop_id", "accept", "birthday"], "title": "CSItemGiftRequest", "type": "object"}}}}}}, "/api/v1/game/guild/rank": {"get": {"operationId": "get~v1.GuildRankView", "summary": "游戏家族排名", "tags": ["v1"], "parameters": [{"name": "page", "schema": {"default": 1, "minimum": 1, "title": "Page", "type": "integer"}, "in": "query"}, {"name": "size", "schema": {"default": 10, "title": "Size", "type": "integer"}, "in": "query"}], "responses": {"default": {"content": {"application/json": {"schema": {"$defs": {"GuildItem": {"properties": {"guildid": {"title": "<PERSON><PERSON>", "type": "integer"}, "GP": {"title": "Gp", "type": "integer"}, "logo": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Logo"}, "logoColor": {"title": "Logocolor", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "logoBG": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Logobg"}, "logoBGColor": {"title": "Logobgcolor", "type": "integer"}, "member": {"title": "Member", "type": "integer"}, "capacity": {"title": "Capacity", "type": "integer"}, "pub_notice": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Pub Notice"}, "alliance_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Alliance Name"}, "leader_name": {"title": "Leader Name", "type": "string"}, "leader": {"title": "Leader", "type": "integer"}}, "required": ["guildid", "GP", "logo", "logoColor", "name", "logoBG", "logoBGColor", "member", "capacity", "pub_notice", "alliance_name", "leader_name", "leader"], "title": "GuildItem", "type": "object"}, "GuildRankResponse": {"properties": {"total": {"title": "Total", "type": "integer"}, "items": {"items": {"$ref": "#/components/schemas/GuildItem"}, "title": "Items", "type": "array"}}, "required": ["total", "items"], "title": "GuildRankResponse", "type": "object"}}, "properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"$ref": "#/components/schemas/GuildRankResponse"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[GuildRankResponse]", "type": "object"}}}, "description": "Default Response"}}}}, "/api/v1/game/character/rank": {"get": {"operationId": "get~v1.CharRankView", "summary": "游戏排行榜", "tags": ["v1"], "parameters": [{"name": "page", "schema": {"default": 1, "minimum": 1, "title": "Page", "type": "integer"}, "in": "query"}, {"name": "size", "schema": {"default": 10, "title": "Size", "type": "integer"}, "in": "query"}, {"name": "job", "schema": {"default": "all", "enum": ["all", "beginner", "warrior", "magician", "bowman", "thief", "pirate", "cygnus", "aran"], "title": "Job", "type": "string"}, "in": "query"}, {"name": "sort", "schema": {"default": "level", "enum": ["level", "fame", "quest", "monsterbook"], "title": "Sort", "type": "string"}, "in": "query"}], "responses": {"default": {"content": {"application/json": {"schema": {"$defs": {"CharRankItem": {"properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "level": {"title": "Level", "type": "integer"}, "job": {"title": "Job", "type": "integer"}, "job_rank": {"title": "Job Rank", "type": "integer"}, "fame": {"title": "Fame", "type": "integer"}, "rank": {"title": "Rank", "type": "integer"}, "job_name": {"default": "", "title": "Job Name", "type": "string"}, "guild_name": {"default": "", "title": "Guild Name", "type": "string"}, "guild": {"anyOf": [{"$ref": "#/components/schemas/GuildItem"}, {"type": "null"}], "default": null}, "quest_count": {"default": 0, "title": "Quest Count", "type": "integer"}, "monster_book": {"default": 0, "title": "Monster Book", "type": "integer"}}, "required": ["id", "name", "level", "job", "job_rank", "fame", "rank"], "title": "CharRankItem", "type": "object"}, "CharRankResponse": {"properties": {"total": {"title": "Total", "type": "integer"}, "items": {"items": {"$ref": "#/components/schemas/CharRankItem"}, "title": "Items", "type": "array"}}, "required": ["total", "items"], "title": "CharRankResponse", "type": "object"}, "GuildItem": {"properties": {"guildid": {"title": "<PERSON><PERSON>", "type": "integer"}, "GP": {"title": "Gp", "type": "integer"}, "logo": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Logo"}, "logoColor": {"title": "Logocolor", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "logoBG": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Logobg"}, "logoBGColor": {"title": "Logobgcolor", "type": "integer"}, "member": {"title": "Member", "type": "integer"}, "capacity": {"title": "Capacity", "type": "integer"}, "pub_notice": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Pub Notice"}, "alliance_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Alliance Name"}, "leader_name": {"title": "Leader Name", "type": "string"}, "leader": {"title": "Leader", "type": "integer"}}, "required": ["guildid", "GP", "logo", "logoColor", "name", "logoBG", "logoBGColor", "member", "capacity", "pub_notice", "alliance_name", "leader_name", "leader"], "title": "GuildItem", "type": "object"}}, "properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"$ref": "#/components/schemas/CharRankResponse"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[CharRankResponse]", "type": "object"}}}, "description": "Default Response"}}}}, "/api/v1/cashshop/type": {"get": {"operationId": "get~v1.CSTypeView", "summary": "获取商城物品类型", "tags": ["v1"], "responses": {"default": {"content": {"application/json": {"schema": {"$defs": {"CSItemType": {"properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}}, "required": ["id", "name"], "title": "CSItemType", "type": "object"}, "CashShopTypeResponse": {"properties": {"items": {"items": {"$ref": "#/components/schemas/CSItemType"}, "title": "Items", "type": "array"}}, "required": ["items"], "title": "CashShopTypeResponse", "type": "object"}}, "properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"$ref": "#/components/schemas/CashShopTypeResponse"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[CashShopTypeResponse]", "type": "object"}}}, "description": "Default Response"}}}}, "/api/v1/cashshop/buy": {"post": {"operationId": "post~v1.CSItemBuyView", "summary": "购买商城物品", "tags": ["v1"], "responses": {"default": {"content": {"application/json": {"schema": {"properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"title": "Data", "type": "null"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[NoneType]", "type": "object"}}}, "description": "Default Response"}}, "requestBody": {"content": {"application/json": {"schema": {"properties": {"shop_id": {"title": "Shop Id", "type": "integer"}, "character_id": {"title": "Character Id", "type": "integer"}}, "required": ["shop_id", "character_id"], "title": "CSItemBuyRequest", "type": "object"}}}}}}, "/api/v1/game/ea": {"post": {"operationId": "post~v1.EAView", "summary": "游戏账号解卡", "tags": ["v1"], "responses": {"default": {"content": {"application/json": {"schema": {"properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"title": "Data", "type": "null"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[NoneType]", "type": "object"}}}, "description": "Default Response"}}}}, "/api/v1/game/status": {"get": {"operationId": "get~v1.<PERSON><PERSON><PERSON>w", "summary": "游戏服务状态", "tags": ["v1"], "responses": {"default": {"content": {"application/json": {"schema": {"$defs": {"GameOnlineResponse": {"properties": {"status": {"title": "Status", "type": "string"}, "invite": {"default": false, "title": "Invite", "type": "boolean"}, "count": {"title": "Count", "type": "integer"}, "characters": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Characters"}}, "required": ["status", "count"], "title": "GameOnlineResponse", "type": "object"}}, "properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"$ref": "#/components/schemas/GameOnlineResponse"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[GameOnlineResponse]", "type": "object"}}}, "description": "Default Response"}}}}, "/api/v1/user/apply_invite": {"post": {"operationId": "post~v1.ApplyInviteView", "summary": "申请邀请码", "tags": ["v1"], "responses": {"default": {"content": {"application/json": {"schema": {"properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"title": "Data", "type": "null"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[NoneType]", "type": "object"}}}, "description": "Default Response"}}}}, "/api/v1/user/checkin": {"post": {"operationId": "post~v1.CheckInView", "summary": "用户签到", "tags": ["v1"], "responses": {"default": {"content": {"application/json": {"schema": {"properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"title": "Data", "type": "null"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[NoneType]", "type": "object"}}}, "description": "Default Response"}}, "requestBody": {"content": {"application/json": {"schema": {"properties": {"character_id": {"title": "Character Id", "type": "integer"}}, "required": ["character_id"], "title": "CheckInRequest", "type": "object"}}}}}}, "/api/v1/auth/refresh": {"post": {"operationId": "post~v1.RefreshToken", "summary": "刷新token", "tags": ["v1"], "responses": {"default": {"content": {"application/json": {"schema": {"$defs": {"LoginResponse": {"properties": {"token": {"title": "Token", "type": "string"}, "refresh_token": {"title": "Refresh <PERSON>", "type": "string"}, "expires_in": {"title": "Expires In", "type": "integer"}, "refresh_expires_in": {"title": "Refresh Expires In", "type": "integer"}, "token_type": {"default": "Bearer", "title": "Token Type", "type": "string"}}, "required": ["token", "refresh_token", "expires_in", "refresh_expires_in"], "title": "LoginResponse", "type": "object"}}, "properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"$ref": "#/components/schemas/LoginResponse"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[LoginResponse]", "type": "object"}}}, "description": "Default Response"}}}}, "/api/v1/library/source": {"get": {"operationId": "get~v1.LibrarySourceView", "summary": "搜索资料库", "tags": ["v1"], "parameters": [{"name": "page", "schema": {"default": 1, "minimum": 1, "title": "Page", "type": "integer"}, "in": "query"}, {"name": "size", "schema": {"default": 10, "title": "Size", "type": "integer"}, "in": "query"}, {"name": "category", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Category"}, "in": "query"}, {"name": "oid", "schema": {"default": -1, "title": "Oid", "type": "integer"}, "in": "query"}], "responses": {"default": {"content": {"application/json": {"schema": {"$defs": {"LibraryQueryResponse": {"properties": {"total": {"title": "Total", "type": "integer"}, "size": {"title": "Size", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "items": {"items": {"type": "object"}, "title": "Items", "type": "array"}}, "required": ["total", "size", "page", "items"], "title": "LibraryQueryResponse", "type": "object"}}, "properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"$ref": "#/components/schemas/LibraryQueryResponse"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[LibraryQueryResponse]", "type": "object"}}}, "description": "Default Response"}}}}, "/api/v1/game/online": {"get": {"operationId": "get~v1.OnlineView", "summary": "游戏服务状态", "tags": ["v1"], "responses": {"default": {"content": {"application/json": {"schema": {"$defs": {"GameOnlineResponse": {"properties": {"status": {"title": "Status", "type": "string"}, "invite": {"default": false, "title": "Invite", "type": "boolean"}, "count": {"title": "Count", "type": "integer"}, "characters": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Characters"}}, "required": ["status", "count"], "title": "GameOnlineResponse", "type": "object"}}, "properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"$ref": "#/components/schemas/GameOnlineResponse"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[GameOnlineResponse]", "type": "object"}}}, "description": "Default Response"}}}}, "/api/v1/cashshop/items": {"get": {"operationId": "get~v1.CSItemListView", "summary": "获取商城物品列表", "tags": ["v1"], "parameters": [{"name": "page", "schema": {"default": 1, "minimum": 1, "title": "Page", "type": "integer"}, "in": "query"}, {"name": "limit", "schema": {"default": 24, "maximum": 100, "minimum": 1, "title": "Limit", "type": "integer"}, "in": "query"}, {"name": "keyword", "schema": {"default": "", "title": "Keyword", "type": "string"}, "in": "query"}, {"name": "category", "schema": {"default": "", "title": "Category", "type": "string"}, "in": "query"}], "responses": {"default": {"content": {"application/json": {"schema": {"$defs": {"CSItem": {"properties": {"id": {"title": "Id", "type": "integer"}, "title": {"title": "Title", "type": "string"}, "desc": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "", "title": "Desc"}, "category": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "", "title": "Category"}, "itemId": {"title": "<PERSON><PERSON><PERSON>", "type": "integer"}, "itemIco": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Itemico"}, "price": {"title": "Price", "type": "integer"}, "currency": {"title": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}, "display": {"title": "Display", "type": "boolean"}, "canBuy": {"title": "Canbuy", "type": "boolean"}, "receiveMethod": {"title": "Receivemethod", "type": "integer"}, "banGift": {"title": "Bangift", "type": "boolean"}, "create_time": {"format": "date-time", "title": "Create Time", "type": "string"}, "start_sale_time": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Start Sale Time"}, "end_sale_time": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "End Sale Time"}, "amount": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Amount"}, "limit_group": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Limit Group"}, "user_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "User Limit"}, "char_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "expiration": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Expiration"}}, "required": ["id", "title", "itemId", "itemIco", "price", "currency", "display", "canBuy", "receiveMethod", "banGift", "create_time", "start_sale_time", "end_sale_time", "amount", "limit_group", "user_limit", "char_limit", "expiration"], "title": "CSItem", "type": "object"}, "CSItemQueryResponse": {"properties": {"total": {"title": "Total", "type": "integer"}, "count": {"title": "Count", "type": "integer"}, "items": {"items": {"$ref": "#/components/schemas/CSItem"}, "title": "Items", "type": "array"}}, "required": ["total", "count", "items"], "title": "CSItemQueryResponse", "type": "object"}}, "properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"$ref": "#/components/schemas/CSItemQueryResponse"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[CSItemQueryResponse]", "type": "object"}}}, "description": "Default Response"}}}}, "/api/v1/user/update_password": {"post": {"operationId": "post~v1.UpdatePwdView", "summary": "修改密码", "tags": ["v1"], "responses": {"default": {"content": {"application/json": {"schema": {"properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"title": "Data", "type": "null"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[NoneType]", "type": "object"}}}, "description": "Default Response"}}, "requestBody": {"content": {"application/json": {"schema": {"properties": {"op": {"title": "Op", "type": "string"}, "password": {"title": "Password", "type": "string"}}, "required": ["op", "password"], "title": "UpdatePwdRequest", "type": "object"}}}}}}, "/api/v1/cashshop/poster": {"get": {"operationId": "get~v1.CSPosterView", "summary": "获取商城首页海报", "tags": ["v1"], "responses": {"default": {"content": {"application/json": {"schema": {"$defs": {"CSPoster": {"properties": {"href": {"default": "", "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "doc": {"default": "", "title": "Doc", "type": "string"}, "redirect": {"default": "", "title": "Redirect", "type": "string"}, "title": {"default": "", "title": "Title", "type": "string"}}, "title": "CS<PERSON>oster", "type": "object"}}, "properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"$ref": "#/components/schemas/CSPoster"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[CSPoster]", "type": "object"}}}, "description": "Default Response"}}}}, "/api/v1/notice/list": {"get": {"operationId": "get~v1.NoticeListView", "summary": "公告列表", "tags": ["v1"], "parameters": [{"name": "page", "schema": {"default": 1, "minimum": 1, "title": "Page", "type": "integer"}, "in": "query"}, {"name": "size", "schema": {"default": 10, "title": "Size", "type": "integer"}, "in": "query"}], "responses": {"default": {"content": {"application/json": {"schema": {"$defs": {"NoticeItem": {"properties": {"id": {"title": "Id", "type": "integer"}, "title": {"title": "Title", "type": "string"}, "content": {"title": "Content", "type": "string"}, "create_time": {"format": "date-time", "title": "Create Time", "type": "string"}}, "required": ["id", "title", "content", "create_time"], "title": "NoticeItem", "type": "object"}, "NoticeListResponse": {"properties": {"items": {"items": {"$ref": "#/components/schemas/NoticeItem"}, "title": "Items", "type": "array"}, "total": {"title": "Total", "type": "integer"}}, "required": ["items", "total"], "title": "NoticeListResponse", "type": "object"}}, "properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"$ref": "#/components/schemas/NoticeListResponse"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[NoticeListResponse]", "type": "object"}}}, "description": "Default Response"}}}}, "/api/v1/user/info": {"get": {"operationId": "get~v1.UserInfoView", "summary": "获取用户信息", "tags": ["v1"], "responses": {"default": {"content": {"application/json": {"schema": {"$defs": {"UserInfoResponse": {"properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "nick": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "", "title": "<PERSON>"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "", "title": "Email"}, "loggedin": {"title": "Loggedin", "type": "integer"}, "lastlogin": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Lastlogin"}, "nxCredit": {"title": "Nxcredit", "type": "integer"}, "maplePoint": {"title": "Maplepoint", "type": "integer"}, "nxPrepaid": {"title": "Nxprepaid", "type": "integer"}, "characterslots": {"title": "Characterslots", "type": "integer"}, "gender": {"title": "Gender", "type": "integer"}, "banned": {"default": 0, "title": "Banned", "type": "integer"}, "banned_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Banned Reason"}, "web_admin": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 0, "title": "Web Admin"}, "rewardpoints": {"title": "Rewardpoints", "type": "integer"}, "votepoints": {"title": "Votepoints", "type": "integer"}, "language": {"title": "Language", "type": "integer"}, "createdat": {"format": "date-time", "title": "Createdat", "type": "string"}}, "required": ["id", "name", "loggedin", "lastlogin", "nxCredit", "maplePoint", "nxPrepaid", "characterslots", "gender", "rewardpoints", "votepoints", "language", "createdat"], "title": "UserInfoResponse", "type": "object"}}, "properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"$ref": "#/components/schemas/UserInfoResponse"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[UserInfoResponse]", "type": "object"}}}, "description": "Default Response"}}}}, "/api/v1/auth/register": {"post": {"operationId": "post~v1.Register<PERSON>iew", "summary": "用户注册", "tags": ["v1"], "responses": {"default": {"content": {"application/json": {"schema": {"properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"title": "Data", "type": "null"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[NoneType]", "type": "object"}}}, "description": "Default Response"}}, "requestBody": {"content": {"application/json": {"schema": {"properties": {"username": {"maxLength": 13, "minLength": 5, "title": "Username", "type": "string"}, "email": {"pattern": "[A-Za-z0-9\\u4e00-\\u9fa5]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$", "title": "Email", "type": "string"}, "code": {"title": "Code", "type": "string"}, "pwd1": {"pattern": "[\\da-zA-Z!@#$%^&*,./|}{()_+=\\-]{6,16}$", "title": "Pwd1", "type": "string"}, "pwd2": {"title": "Pwd2", "type": "string"}, "birthday": {"format": "date", "title": "Birthday", "type": "string"}, "invitation_code": {"default": "", "title": "Invitation Code", "type": "string"}, "captcha": {"default": "", "title": "<PERSON><PERSON>", "type": "string"}}, "required": ["username", "email", "code", "pwd1", "pwd2", "birthday"], "title": "RegisterRequest", "type": "object"}}}}}}, "/api/v1/vote": {"post": {"operationId": "post~v1.<PERSON><PERSON><PERSON>", "summary": "用户投票", "tags": ["v1"], "responses": {"default": {"content": {"application/json": {"schema": {"$defs": {"VoteRedirectResponse": {"properties": {"url": {"title": "Url", "type": "string"}}, "required": ["url"], "title": "VoteRedirectResponse", "type": "object"}}, "properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"$ref": "#/components/schemas/VoteRedirectResponse"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[VoteRedirectResponse]", "type": "object"}}}, "description": "Default Response"}}, "requestBody": {"content": {"application/json": {"schema": {"properties": {"username": {"title": "Username", "type": "string"}}, "required": ["username"], "title": "UserName", "type": "object"}}}}}}, "/api/v1/library/search": {"get": {"operationId": "get~v1.LibrarySearchView", "summary": "搜索资料库", "tags": ["v1"], "parameters": [{"name": "page", "schema": {"default": 1, "minimum": 1, "title": "Page", "type": "integer"}, "in": "query"}, {"name": "size", "schema": {"default": 10, "title": "Size", "type": "integer"}, "in": "query"}, {"name": "category", "schema": {"default": "all", "title": "Category", "type": "string"}, "in": "query"}, {"name": "query", "schema": {"default": "", "title": "Query", "type": "string"}, "in": "query"}], "responses": {"default": {"content": {"application/json": {"schema": {"$defs": {"LibraryQueryResponse": {"properties": {"total": {"title": "Total", "type": "integer"}, "size": {"title": "Size", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "items": {"items": {"type": "object"}, "title": "Items", "type": "array"}}, "required": ["total", "size", "page", "items"], "title": "LibraryQueryResponse", "type": "object"}}, "properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"$ref": "#/components/schemas/LibraryQueryResponse"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[LibraryQueryResponse]", "type": "object"}}}, "description": "Default Response"}}}}, "/api/v1/auth/login": {"post": {"operationId": "post~v1.<PERSON><PERSON><PERSON>", "summary": "用户登录", "tags": ["v1"], "responses": {"default": {"content": {"application/json": {"schema": {"$defs": {"LoginResponse": {"properties": {"token": {"title": "Token", "type": "string"}, "refresh_token": {"title": "Refresh <PERSON>", "type": "string"}, "expires_in": {"title": "Expires In", "type": "integer"}, "refresh_expires_in": {"title": "Refresh Expires In", "type": "integer"}, "token_type": {"default": "Bearer", "title": "Token Type", "type": "string"}}, "required": ["token", "refresh_token", "expires_in", "refresh_expires_in"], "title": "LoginResponse", "type": "object"}}, "properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"$ref": "#/components/schemas/LoginResponse"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[LoginResponse]", "type": "object"}}}, "description": "Default Response"}}, "requestBody": {"content": {"application/json": {"schema": {"properties": {"username": {"title": "Username", "type": "string"}, "password": {"title": "Password", "type": "string"}}, "required": ["username", "password"], "title": "LoginRequest", "type": "object"}}}}}}, "/notice/{nid}": {"get": {"operationId": "get~render.notice_id", "summary": "Notice Id", "tags": ["render"], "parameters": [{"name": "nid", "schema": {"type": "string"}, "required": true, "in": "path"}], "responses": {"default": {"description": "OK"}}}}, "/api/avatar/{character_id}": {"get": {"operationId": "get~render.avatar", "summary": "Avatar", "tags": ["render"], "parameters": [{"name": "character_id", "schema": {"type": "integer", "format": "int32"}, "required": true, "in": "path"}], "responses": {"default": {"description": "OK"}}}}, "/api/v1/character/{character_id}/equip": {"get": {"operationId": "get~v1.CharEquipView", "summary": "获取角色装备物品", "tags": ["v1"], "parameters": [{"name": "character_id", "schema": {"type": "integer", "format": "int32"}, "required": true, "in": "path"}], "responses": {"default": {"content": {"application/json": {"schema": {"properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"title": "Data", "type": "null"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[NoneType]", "type": "object"}}}, "description": "Default Response"}}}}, "/api/v1/character/{character_id}": {"get": {"operationId": "get~v1.<PERSON><PERSON><PERSON><PERSON><PERSON>", "summary": "获取用户指定角色", "tags": ["v1"], "parameters": [{"name": "character_id", "schema": {"type": "integer", "format": "int32"}, "required": true, "in": "path"}], "responses": {"default": {"content": {"application/json": {"schema": {"$defs": {"CharInfo": {"properties": {"id": {"title": "Id", "type": "integer"}, "accountid": {"title": "Accountid", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "level": {"title": "Level", "type": "integer"}, "str": {"title": "Str", "type": "integer"}, "dex": {"title": "<PERSON>", "type": "integer"}, "int": {"title": "Int", "type": "integer"}, "luk": {"title": "Luk", "type": "integer"}, "maxhp": {"title": "<PERSON><PERSON>", "type": "integer"}, "maxmp": {"title": "<PERSON><PERSON>", "type": "integer"}, "gender": {"title": "Gender", "type": "integer"}, "map": {"title": "Map", "type": "integer"}, "rank": {"title": "Rank", "type": "integer"}, "meso": {"title": "Meso", "type": "integer"}, "job": {"title": "Job", "type": "integer"}, "fame": {"title": "Fame", "type": "integer"}, "hair": {"title": "Hair", "type": "integer"}, "face": {"title": "Face", "type": "integer"}, "skincolor": {"title": "Skincolor", "type": "integer"}, "guildid": {"title": "<PERSON><PERSON>", "type": "integer"}, "createdate": {"format": "date-time", "title": "Createdate", "type": "string"}, "lastLogoutTime": {"format": "date-time", "title": "Lastlogouttime", "type": "string"}, "jailexpire": {"title": "Jailexpire", "type": "integer"}}, "required": ["id", "accountid", "name", "level", "str", "dex", "int", "luk", "maxhp", "maxmp", "gender", "map", "rank", "meso", "job", "fame", "hair", "face", "skincolor", "guildid", "createdate", "lastLogoutTime", "jailexpire"], "title": "CharInfo", "type": "object"}}, "properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"$ref": "#/components/schemas/CharInfo"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[CharInfo]", "type": "object"}}}, "description": "Default Response"}}}}, "/api/v1/game/character/avatar/{character_id}": {"get": {"operationId": "get~v1.CharAvatar<PERSON>iew", "summary": "获取角色简图", "tags": ["v1"], "parameters": [{"name": "character_id", "schema": {"type": "integer", "format": "int32"}, "required": true, "in": "path"}], "responses": {"default": {"content": {"application/json": {"schema": {"$defs": {"CharAvatarResponse": {"properties": {"character_id": {"title": "Character Id", "type": "integer"}, "image": {"title": "Image", "type": "string"}}, "required": ["character_id", "image"], "title": "CharAvatarResponse", "type": "object"}}, "properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"$ref": "#/components/schemas/CharAvatarResponse"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[CharAvatarResponse]", "type": "object"}}}, "description": "Default Response"}}}}, "/api/v1/character/{character_id}/items": {"get": {"operationId": "get~v1.CharItemListView", "summary": "获取角色背包物品", "tags": ["v1"], "parameters": [{"name": "character_id", "schema": {"type": "integer", "format": "int32"}, "required": true, "in": "path"}], "responses": {"default": {"content": {"application/json": {"schema": {"$defs": {"CharItemResponse": {"properties": {"items": {"items": {"$ref": "#/components/schemas/InvItemModel"}, "title": "Items", "type": "array"}}, "required": ["items"], "title": "CharItemResponse", "type": "object"}, "InvItemModel": {"properties": {"inventoryitemid": {"title": "Inventoryitemid", "type": "integer"}, "type": {"title": "Type", "type": "integer"}, "characterid": {"title": "<PERSON><PERSON>", "type": "integer"}, "itemid": {"title": "<PERSON><PERSON><PERSON>", "type": "integer"}, "inventorytype": {"title": "Inventorytype", "type": "integer"}, "position": {"title": "Position", "type": "integer"}, "quantity": {"title": "Quantity", "type": "integer"}, "owner": {"title": "Owner", "type": "string"}, "petid": {"title": "<PERSON><PERSON>", "type": "integer"}, "flag": {"title": "Flag", "type": "integer"}, "expiration": {"title": "Expiration", "type": "integer"}, "giftFrom": {"title": "Giftfrom", "type": "string"}}, "required": ["inventoryitemid", "type", "characterid", "itemid", "inventorytype", "position", "quantity", "owner", "petid", "flag", "expiration", "giftFrom"], "title": "InvItemModel", "type": "object"}}, "properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"$ref": "#/components/schemas/CharItemResponse"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[CharItemResponse]", "type": "object"}}}, "description": "Default Response"}}}}, "/api/v1/notice/{nid}": {"get": {"operationId": "get~v1.Notice<PERSON>iew", "summary": "公告详情", "tags": ["v1"], "parameters": [{"name": "nid", "schema": {"type": "integer", "format": "int32"}, "required": true, "in": "path"}], "responses": {"default": {"content": {"application/json": {"schema": {"$defs": {"NoticeItem": {"properties": {"id": {"title": "Id", "type": "integer"}, "title": {"title": "Title", "type": "string"}, "content": {"title": "Content", "type": "string"}, "create_time": {"format": "date-time", "title": "Create Time", "type": "string"}}, "required": ["id", "title", "content", "create_time"], "title": "NoticeItem", "type": "object"}}, "properties": {"response_id": {"format": "uuid", "title": "Response Id", "type": "string"}, "code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "data": {"$ref": "#/components/schemas/NoticeItem"}}, "required": ["response_id", "code", "message", "data"], "title": "NormalResponse[NoticeItem]", "type": "object"}}}, "description": "Default Response"}}}}}, "tags": [{"name": "render"}, {"name": "v1"}], "servers": [], "security": [], "components": {"schemas": {"DocHelpResponse": {"properties": {"title": {"title": "Title", "type": "string"}, "create_time": {"title": "Create Time", "type": "string"}, "content": {"title": "Content", "type": "string"}}, "required": ["title", "create_time", "content"], "title": "DocHelpResponse", "type": "object"}, "LoginResponse": {"properties": {"token": {"title": "Token", "type": "string"}, "refresh_token": {"title": "Refresh <PERSON>", "type": "string"}, "expires_in": {"title": "Expires In", "type": "integer"}, "refresh_expires_in": {"title": "Refresh Expires In", "type": "integer"}, "token_type": {"default": "Bearer", "title": "Token Type", "type": "string"}}, "required": ["token", "refresh_token", "expires_in", "refresh_expires_in"], "title": "LoginResponse", "type": "object"}, "NoticeListResponse": {"$defs": {"NoticeItem": {"properties": {"id": {"title": "Id", "type": "integer"}, "title": {"title": "Title", "type": "string"}, "content": {"title": "Content", "type": "string"}, "create_time": {"format": "date-time", "title": "Create Time", "type": "string"}}, "required": ["id", "title", "content", "create_time"], "title": "NoticeItem", "type": "object"}}, "properties": {"items": {"items": {"$ref": "#/components/schemas/NoticeItem"}, "title": "Items", "type": "array"}, "total": {"title": "Total", "type": "integer"}}, "required": ["items", "total"], "title": "NoticeListResponse", "type": "object"}, "NoticeItem": {"properties": {"id": {"title": "Id", "type": "integer"}, "title": {"title": "Title", "type": "string"}, "content": {"title": "Content", "type": "string"}, "create_time": {"format": "date-time", "title": "Create Time", "type": "string"}}, "required": ["id", "title", "content", "create_time"], "title": "NoticeItem", "type": "object"}, "CashShopTypeResponse": {"$defs": {"CSItemType": {"properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}}, "required": ["id", "name"], "title": "CSItemType", "type": "object"}}, "properties": {"items": {"items": {"$ref": "#/components/schemas/CSItemType"}, "title": "Items", "type": "array"}}, "required": ["items"], "title": "CashShopTypeResponse", "type": "object"}, "CSItemType": {"properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}}, "required": ["id", "name"], "title": "CSItemType", "type": "object"}, "CSPoster": {"properties": {"href": {"default": "", "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "doc": {"default": "", "title": "Doc", "type": "string"}, "redirect": {"default": "", "title": "Redirect", "type": "string"}, "title": {"default": "", "title": "Title", "type": "string"}}, "title": "CS<PERSON>oster", "type": "object"}, "CSItemQueryResponse": {"$defs": {"CSItem": {"properties": {"id": {"title": "Id", "type": "integer"}, "title": {"title": "Title", "type": "string"}, "desc": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "", "title": "Desc"}, "category": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "", "title": "Category"}, "itemId": {"title": "<PERSON><PERSON><PERSON>", "type": "integer"}, "itemIco": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Itemico"}, "price": {"title": "Price", "type": "integer"}, "currency": {"title": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}, "display": {"title": "Display", "type": "boolean"}, "canBuy": {"title": "Canbuy", "type": "boolean"}, "receiveMethod": {"title": "Receivemethod", "type": "integer"}, "banGift": {"title": "Bangift", "type": "boolean"}, "create_time": {"format": "date-time", "title": "Create Time", "type": "string"}, "start_sale_time": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Start Sale Time"}, "end_sale_time": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "End Sale Time"}, "amount": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Amount"}, "limit_group": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Limit Group"}, "user_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "User Limit"}, "char_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "expiration": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Expiration"}}, "required": ["id", "title", "itemId", "itemIco", "price", "currency", "display", "canBuy", "receiveMethod", "banGift", "create_time", "start_sale_time", "end_sale_time", "amount", "limit_group", "user_limit", "char_limit", "expiration"], "title": "CSItem", "type": "object"}}, "properties": {"total": {"title": "Total", "type": "integer"}, "count": {"title": "Count", "type": "integer"}, "items": {"items": {"$ref": "#/components/schemas/CSItem"}, "title": "Items", "type": "array"}}, "required": ["total", "count", "items"], "title": "CSItemQueryResponse", "type": "object"}, "CSItem": {"properties": {"id": {"title": "Id", "type": "integer"}, "title": {"title": "Title", "type": "string"}, "desc": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "", "title": "Desc"}, "category": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "", "title": "Category"}, "itemId": {"title": "<PERSON><PERSON><PERSON>", "type": "integer"}, "itemIco": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Itemico"}, "price": {"title": "Price", "type": "integer"}, "currency": {"title": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}, "display": {"title": "Display", "type": "boolean"}, "canBuy": {"title": "Canbuy", "type": "boolean"}, "receiveMethod": {"title": "Receivemethod", "type": "integer"}, "banGift": {"title": "Bangift", "type": "boolean"}, "create_time": {"format": "date-time", "title": "Create Time", "type": "string"}, "start_sale_time": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Start Sale Time"}, "end_sale_time": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "End Sale Time"}, "amount": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Amount"}, "limit_group": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Limit Group"}, "user_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "User Limit"}, "char_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "expiration": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Expiration"}}, "required": ["id", "title", "itemId", "itemIco", "price", "currency", "display", "canBuy", "receiveMethod", "banGift", "create_time", "start_sale_time", "end_sale_time", "amount", "limit_group", "user_limit", "char_limit", "expiration"], "title": "CSItem", "type": "object"}, "LibraryQueryResponse": {"properties": {"total": {"title": "Total", "type": "integer"}, "size": {"title": "Size", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "items": {"items": {"type": "object"}, "title": "Items", "type": "array"}}, "required": ["total", "size", "page", "items"], "title": "LibraryQueryResponse", "type": "object"}, "VoteRedirectResponse": {"properties": {"url": {"title": "Url", "type": "string"}}, "required": ["url"], "title": "VoteRedirectResponse", "type": "object"}, "GameOnlineResponse": {"properties": {"status": {"title": "Status", "type": "string"}, "invite": {"default": false, "title": "Invite", "type": "boolean"}, "count": {"title": "Count", "type": "integer"}, "characters": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Characters"}}, "required": ["status", "count"], "title": "GameOnlineResponse", "type": "object"}, "CharRankResponse": {"$defs": {"CharRankItem": {"properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "level": {"title": "Level", "type": "integer"}, "job": {"title": "Job", "type": "integer"}, "job_rank": {"title": "Job Rank", "type": "integer"}, "fame": {"title": "Fame", "type": "integer"}, "rank": {"title": "Rank", "type": "integer"}, "job_name": {"default": "", "title": "Job Name", "type": "string"}, "guild_name": {"default": "", "title": "Guild Name", "type": "string"}, "guild": {"anyOf": [{"$ref": "#/components/schemas/GuildItem"}, {"type": "null"}], "default": null}, "quest_count": {"default": 0, "title": "Quest Count", "type": "integer"}, "monster_book": {"default": 0, "title": "Monster Book", "type": "integer"}}, "required": ["id", "name", "level", "job", "job_rank", "fame", "rank"], "title": "CharRankItem", "type": "object"}, "GuildItem": {"properties": {"guildid": {"title": "<PERSON><PERSON>", "type": "integer"}, "GP": {"title": "Gp", "type": "integer"}, "logo": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Logo"}, "logoColor": {"title": "Logocolor", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "logoBG": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Logobg"}, "logoBGColor": {"title": "Logobgcolor", "type": "integer"}, "member": {"title": "Member", "type": "integer"}, "capacity": {"title": "Capacity", "type": "integer"}, "pub_notice": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Pub Notice"}, "alliance_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Alliance Name"}, "leader_name": {"title": "Leader Name", "type": "string"}, "leader": {"title": "Leader", "type": "integer"}}, "required": ["guildid", "GP", "logo", "logoColor", "name", "logoBG", "logoBGColor", "member", "capacity", "pub_notice", "alliance_name", "leader_name", "leader"], "title": "GuildItem", "type": "object"}}, "properties": {"total": {"title": "Total", "type": "integer"}, "items": {"items": {"$ref": "#/components/schemas/CharRankItem"}, "title": "Items", "type": "array"}}, "required": ["total", "items"], "title": "CharRankResponse", "type": "object"}, "CharRankItem": {"$defs": {"GuildItem": {"properties": {"guildid": {"title": "<PERSON><PERSON>", "type": "integer"}, "GP": {"title": "Gp", "type": "integer"}, "logo": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Logo"}, "logoColor": {"title": "Logocolor", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "logoBG": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Logobg"}, "logoBGColor": {"title": "Logobgcolor", "type": "integer"}, "member": {"title": "Member", "type": "integer"}, "capacity": {"title": "Capacity", "type": "integer"}, "pub_notice": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Pub Notice"}, "alliance_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Alliance Name"}, "leader_name": {"title": "Leader Name", "type": "string"}, "leader": {"title": "Leader", "type": "integer"}}, "required": ["guildid", "GP", "logo", "logoColor", "name", "logoBG", "logoBGColor", "member", "capacity", "pub_notice", "alliance_name", "leader_name", "leader"], "title": "GuildItem", "type": "object"}}, "properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "level": {"title": "Level", "type": "integer"}, "job": {"title": "Job", "type": "integer"}, "job_rank": {"title": "Job Rank", "type": "integer"}, "fame": {"title": "Fame", "type": "integer"}, "rank": {"title": "Rank", "type": "integer"}, "job_name": {"default": "", "title": "Job Name", "type": "string"}, "guild_name": {"default": "", "title": "Guild Name", "type": "string"}, "guild": {"anyOf": [{"$ref": "#/components/schemas/GuildItem"}, {"type": "null"}], "default": null}, "quest_count": {"default": 0, "title": "Quest Count", "type": "integer"}, "monster_book": {"default": 0, "title": "Monster Book", "type": "integer"}}, "required": ["id", "name", "level", "job", "job_rank", "fame", "rank"], "title": "CharRankItem", "type": "object"}, "GuildItem": {"properties": {"guildid": {"title": "<PERSON><PERSON>", "type": "integer"}, "GP": {"title": "Gp", "type": "integer"}, "logo": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Logo"}, "logoColor": {"title": "Logocolor", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "logoBG": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Logobg"}, "logoBGColor": {"title": "Logobgcolor", "type": "integer"}, "member": {"title": "Member", "type": "integer"}, "capacity": {"title": "Capacity", "type": "integer"}, "pub_notice": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Pub Notice"}, "alliance_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Alliance Name"}, "leader_name": {"title": "Leader Name", "type": "string"}, "leader": {"title": "Leader", "type": "integer"}}, "required": ["guildid", "GP", "logo", "logoColor", "name", "logoBG", "logoBGColor", "member", "capacity", "pub_notice", "alliance_name", "leader_name", "leader"], "title": "GuildItem", "type": "object"}, "GuildRankResponse": {"$defs": {"GuildItem": {"properties": {"guildid": {"title": "<PERSON><PERSON>", "type": "integer"}, "GP": {"title": "Gp", "type": "integer"}, "logo": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Logo"}, "logoColor": {"title": "Logocolor", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "logoBG": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Logobg"}, "logoBGColor": {"title": "Logobgcolor", "type": "integer"}, "member": {"title": "Member", "type": "integer"}, "capacity": {"title": "Capacity", "type": "integer"}, "pub_notice": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Pub Notice"}, "alliance_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Alliance Name"}, "leader_name": {"title": "Leader Name", "type": "string"}, "leader": {"title": "Leader", "type": "integer"}}, "required": ["guildid", "GP", "logo", "logoColor", "name", "logoBG", "logoBGColor", "member", "capacity", "pub_notice", "alliance_name", "leader_name", "leader"], "title": "GuildItem", "type": "object"}}, "properties": {"total": {"title": "Total", "type": "integer"}, "items": {"items": {"$ref": "#/components/schemas/GuildItem"}, "title": "Items", "type": "array"}}, "required": ["total", "items"], "title": "GuildRankResponse", "type": "object"}, "CharAvatarResponse": {"properties": {"character_id": {"title": "Character Id", "type": "integer"}, "image": {"title": "Image", "type": "string"}}, "required": ["character_id", "image"], "title": "CharAvatarResponse", "type": "object"}, "UserInfoResponse": {"properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "nick": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "", "title": "<PERSON>"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "", "title": "Email"}, "loggedin": {"title": "Loggedin", "type": "integer"}, "lastlogin": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Lastlogin"}, "nxCredit": {"title": "Nxcredit", "type": "integer"}, "maplePoint": {"title": "Maplepoint", "type": "integer"}, "nxPrepaid": {"title": "Nxprepaid", "type": "integer"}, "characterslots": {"title": "Characterslots", "type": "integer"}, "gender": {"title": "Gender", "type": "integer"}, "banned": {"default": 0, "title": "Banned", "type": "integer"}, "banned_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Banned Reason"}, "web_admin": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 0, "title": "Web Admin"}, "rewardpoints": {"title": "Rewardpoints", "type": "integer"}, "votepoints": {"title": "Votepoints", "type": "integer"}, "language": {"title": "Language", "type": "integer"}, "createdat": {"format": "date-time", "title": "Createdat", "type": "string"}}, "required": ["id", "name", "loggedin", "lastlogin", "nxCredit", "maplePoint", "nxPrepaid", "characterslots", "gender", "rewardpoints", "votepoints", "language", "createdat"], "title": "UserInfoResponse", "type": "object"}, "CharListResponse": {"$defs": {"CharInfo": {"properties": {"id": {"title": "Id", "type": "integer"}, "accountid": {"title": "Accountid", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "level": {"title": "Level", "type": "integer"}, "str": {"title": "Str", "type": "integer"}, "dex": {"title": "<PERSON>", "type": "integer"}, "int": {"title": "Int", "type": "integer"}, "luk": {"title": "Luk", "type": "integer"}, "maxhp": {"title": "<PERSON><PERSON>", "type": "integer"}, "maxmp": {"title": "<PERSON><PERSON>", "type": "integer"}, "gender": {"title": "Gender", "type": "integer"}, "map": {"title": "Map", "type": "integer"}, "rank": {"title": "Rank", "type": "integer"}, "meso": {"title": "Meso", "type": "integer"}, "job": {"title": "Job", "type": "integer"}, "fame": {"title": "Fame", "type": "integer"}, "hair": {"title": "Hair", "type": "integer"}, "face": {"title": "Face", "type": "integer"}, "skincolor": {"title": "Skincolor", "type": "integer"}, "guildid": {"title": "<PERSON><PERSON>", "type": "integer"}, "createdate": {"format": "date-time", "title": "Createdate", "type": "string"}, "lastLogoutTime": {"format": "date-time", "title": "Lastlogouttime", "type": "string"}, "jailexpire": {"title": "Jailexpire", "type": "integer"}}, "required": ["id", "accountid", "name", "level", "str", "dex", "int", "luk", "maxhp", "maxmp", "gender", "map", "rank", "meso", "job", "fame", "hair", "face", "skincolor", "guildid", "createdate", "lastLogoutTime", "jailexpire"], "title": "CharInfo", "type": "object"}}, "properties": {"items": {"items": {"$ref": "#/components/schemas/CharInfo"}, "title": "Items", "type": "array"}}, "required": ["items"], "title": "CharListResponse", "type": "object"}, "CharInfo": {"properties": {"id": {"title": "Id", "type": "integer"}, "accountid": {"title": "Accountid", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "level": {"title": "Level", "type": "integer"}, "str": {"title": "Str", "type": "integer"}, "dex": {"title": "<PERSON>", "type": "integer"}, "int": {"title": "Int", "type": "integer"}, "luk": {"title": "Luk", "type": "integer"}, "maxhp": {"title": "<PERSON><PERSON>", "type": "integer"}, "maxmp": {"title": "<PERSON><PERSON>", "type": "integer"}, "gender": {"title": "Gender", "type": "integer"}, "map": {"title": "Map", "type": "integer"}, "rank": {"title": "Rank", "type": "integer"}, "meso": {"title": "Meso", "type": "integer"}, "job": {"title": "Job", "type": "integer"}, "fame": {"title": "Fame", "type": "integer"}, "hair": {"title": "Hair", "type": "integer"}, "face": {"title": "Face", "type": "integer"}, "skincolor": {"title": "Skincolor", "type": "integer"}, "guildid": {"title": "<PERSON><PERSON>", "type": "integer"}, "createdate": {"format": "date-time", "title": "Createdate", "type": "string"}, "lastLogoutTime": {"format": "date-time", "title": "Lastlogouttime", "type": "string"}, "jailexpire": {"title": "Jailexpire", "type": "integer"}}, "required": ["id", "accountid", "name", "level", "str", "dex", "int", "luk", "maxhp", "maxmp", "gender", "map", "rank", "meso", "job", "fame", "hair", "face", "skincolor", "guildid", "createdate", "lastLogoutTime", "jailexpire"], "title": "CharInfo", "type": "object"}, "CharItemResponse": {"$defs": {"InvItemModel": {"properties": {"inventoryitemid": {"title": "Inventoryitemid", "type": "integer"}, "type": {"title": "Type", "type": "integer"}, "characterid": {"title": "<PERSON><PERSON>", "type": "integer"}, "itemid": {"title": "<PERSON><PERSON><PERSON>", "type": "integer"}, "inventorytype": {"title": "Inventorytype", "type": "integer"}, "position": {"title": "Position", "type": "integer"}, "quantity": {"title": "Quantity", "type": "integer"}, "owner": {"title": "Owner", "type": "string"}, "petid": {"title": "<PERSON><PERSON>", "type": "integer"}, "flag": {"title": "Flag", "type": "integer"}, "expiration": {"title": "Expiration", "type": "integer"}, "giftFrom": {"title": "Giftfrom", "type": "string"}}, "required": ["inventoryitemid", "type", "characterid", "itemid", "inventorytype", "position", "quantity", "owner", "petid", "flag", "expiration", "giftFrom"], "title": "InvItemModel", "type": "object"}}, "properties": {"items": {"items": {"$ref": "#/components/schemas/InvItemModel"}, "title": "Items", "type": "array"}}, "required": ["items"], "title": "CharItemResponse", "type": "object"}, "InvItemModel": {"properties": {"inventoryitemid": {"title": "Inventoryitemid", "type": "integer"}, "type": {"title": "Type", "type": "integer"}, "characterid": {"title": "<PERSON><PERSON>", "type": "integer"}, "itemid": {"title": "<PERSON><PERSON><PERSON>", "type": "integer"}, "inventorytype": {"title": "Inventorytype", "type": "integer"}, "position": {"title": "Position", "type": "integer"}, "quantity": {"title": "Quantity", "type": "integer"}, "owner": {"title": "Owner", "type": "string"}, "petid": {"title": "<PERSON><PERSON>", "type": "integer"}, "flag": {"title": "Flag", "type": "integer"}, "expiration": {"title": "Expiration", "type": "integer"}, "giftFrom": {"title": "Giftfrom", "type": "string"}}, "required": ["inventoryitemid", "type", "characterid", "itemid", "inventorytype", "position", "quantity", "owner", "petid", "flag", "expiration", "giftFrom"], "title": "InvItemModel", "type": "object"}}}}