{"name": "magicms", "version": "0.1.0", "private": true, "dependencies": {"@babel/helper-compilation-targets": "^7.18.9", "@testing-library/jest-dom": "^5.11.9", "@testing-library/react": "^11.2.5", "@testing-library/user-event": "^12.8.3", "@types/jest": "^29.5.13", "@types/node": "^22.7.5", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "axios": "^0.28.0", "cos-nodejs-sdk-v5": "^2.10.3", "i18next": "^20.2.2", "i18next-browser-languagedetector": "^6.1.0", "i18next-http-backend": "^1.2.2", "qs": "^6.10.0", "react": "^17.0.1", "react-dom": "^17.0.1", "react-i18next": "^11.8.15", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "react-scripts": "^5.0.1", "tencentcloud-sdk-nodejs": "^4.0.20", "typescript": "^4.9.5", "web-vitals": "^1.1.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}