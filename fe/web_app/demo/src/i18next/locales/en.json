{"home": {"title": "MagicMS - Enjoy the MapleStory", "Welcome": "Welcome to MagicMS!", "introduce1": "A v83 server with modern gameplay mechanics and unique custom features", "trait1": "Global monster drop coupons, white scrolls and chaos scrolls", "trait2": "Equipment level system", "trait3": "Scroll attribute double bonus, chaos scroll does not reduce attribute", "trait4": "Dark frenzy event: double the health of monsters and triple the number of monsters", "trait5": "After level 160, you can freely switch between multiple branches of the same job, the highest level is 255", "trait6": "After the magician is strengthened at level 30, he will receive the blessing of the permanent buff Hans", "trait7": "The <PERSON> gains the permanent buff <PERSON>'s blessing after level 30", "trait8": "<PERSON><PERSON> gains the permanent buff <PERSON><PERSON>'s Faith after strengthening level 160", "trait9": "More modifications to enhance the game experience", "GameUpdate": "Game update: ", "WebUpdate": "Website update: ", "browser": "It is recommended to use Chrome or Edge to visit the website, other browsers may have compatibility issues and cannot use the website functions normally", "introduce2": "/static/images/en/bH0Md3a.png", "introduce3": "/static/images/en/7FtPg74.png", "introduce4": "/static/images/en/R4ONBHK.png", "introduce5": "/static/images/en/72VGCnn.png"}, "download": {"title": "MagicMS - Downloads", "center": "Downloads", "contain1": "Including 1280x720 HD and Normal Client", "contain2": "（Update on January 25）", "google": "Google Drive", "caiyun1": "Caiyun Drive", "caiyun2": "code: RtDU", "update1": "Update Software", "update2": "(Must be logged in)", "189_1": "189 Drive", "189_2": "code: h5kf", "invite_message": "MagicMS has enabled invitation code registration, make sure you have an account before downloading the client to avoid wasting time.", "language_message": "MagicMS only provides English support for a small number of game contents. If you do not have a Chinese environment, you may not be able to get a good game experience.", "contact_help1": "If you cannot get the client through the above channels, you can contact the public email", "contact_help2": " for help", "install_help0": "Installation Instructions:", "install_help1": "1. Extract the ZIP somewhere", "install_help2": "2. Run update.exe, check if the client is up to date", "install_help3": "2. Run \"切换客户端.bat\" and follow the prompts to switch to the target interface mode", "install_help4": "3. Run MapleStory.exe or MapleStoryHD.exe", "solution_btn": "Click here for solutions to common issues", "solution_q1": "Windows Defender: <PERSON><PERSON><PERSON><PERSON> found", "solution_a1_1": "Because the removal of the upper limit of attributes has modified the client binary file, it may be falsely reported as a Trojan horse program. If there is doubt about the program's security, it can be run on an isolated virtual machine.", "solution_a1_2": "solution: Set the exclusion items or exclusion folders of the corresponding antivirus software.", "solution_q2": "Runtime or DLL error", "solution_a2_1": "If you see an error related to DLL files, you are most likely missing Microsoft Visual C++ Redistributable.", "solution_a2_2": "Page", "solution_a2_3": " Download the VC run-time library collection, or you can download it from Microsoft's official website.", "solution_q3": "When I run MapleStory.exe, nothing happens.", "solution_a3": "Check your Task Manager to see if no MapleStory process is still running. If so, end the process. If that doesn't work, try restarting your computer completely. (Do not use fast boot/shutdown, use 'restart').", "solution_q4": "The login interface and the mall interface display abnormally", "solution_a4": "Before running the corresponding client, make sure to use the 切换客户端.bat in the client to switch UI.wz to the corresponding mode", "solution_q5": "Error Code :-2147467259", "solution_a5": "Change the screen resolution and try again. After successfully entering the login interface, you can change the resolution back to the original", "solution_q6": "Error Code: -2147221008", "solution_a6_0": "Try the following methods one by one:", "solution_a6_1": "1.Right-click MapleStory.exe — Properties — Set the compatibility mode — open/close", "solution_a6_2": "2.Right-click MapleStory.exe — Properties — Set the compatibility mode — Disable full screen optimization", "solution_a6_3": "3.This error can usually be avoided after restarting", "solution_q7": "Error Code: (The parameter is incorrect)", "solution_a7": "Right-click MapleStory.exe > Properties > Set the compatibility mode to Windows XP (Service Pack 2)", "solution_q8": "Error Code: (Unspecified error)", "solution_a8": "Check the refresh rate of your display monitor. MapleStory works best on 60 Hz refresh rate. MapleStory will NOT work if your monitor is set to 59 Hz.", "solution_q9": "The game client suddenly disappeared without any prompts", "solution_a9_1": "Some extreme environments have compatibility issues, please contact ", "solution_a9_2": "Developer", "solution_q10": "Still not working??", "solution_a10_1": "Ask the developer through the public email", "solution_a10_2": ", we will be happy to help you.", "solution_q11": "Pets can't take medicine", "solution_a11_1": "1. Re-decompress the client and add the directory to the whitelist to prevent third-party protection software from deleting client dependent files", "solution_a11_2": "2. Clean boot Windows, see", "solution_a11_3": "Microsoft Document", "solution_a11_4": "3. you can try to use a virtual machine to run the game"}, "Rank": {"title": "Rankings", "thead_1": "Rank", "thead_2": "Picture", "thead_3": "Main", "thead_4": "Job", "thead_5": "Level", "thead_6": "Fame", "thead_7": "Quest", "thead_8": "<PERSON><PERSON>", "thead_9": "Guild", "thead_10": "Leader", "thead_11": "Scale", "thead_12": "GP", "thead_13": "Notice", "rank_count": "Results found:", "first_page": "It's already the first page", "last_page": "No more data", "search_name": "Name"}, "Common": {"server_error": "Server response error", "search": "Search", "not_supply_search": "The search function is temporarily closed", "gift": "gift", "buy": "buy", "confirm": "confirm", "cancel": "cancel", "birthday": "birthday", "days": "days", "unlimited": "unlimited", "adequate": "adequate", "price": "Price", "first_page": "It's already the first page", "last_page": "No more data", "all": "All", "status_title": "Server Info", "status": "Server Status", "normal": "Online", "abnormal": "offline", "online": "Players Online", "server_time": "Server Time", "version": "Version", "exp_rate": "Experience Rate", "meso_rate": "Meso Rate", "drop_rate": "Drop Rate", "boss_rate": "Boss Rate", "quest_rate": "Quest Rate", "quest_rate_info": "Custom", "web_quote": "<p style=\"color:orange\">The page design comes from <a href=\"https://storyms.net/\">StoryMS</a>, and the copyright belongs to the original author</p>", "checkin": "Checkin", "checkin_text1": "Are you sure you want to choose ", "checkin_text2": "?", "checkin_success": "Action Succeeded", "checkin_failure": "Action Failed", "login_info": "Login Successful", "login": "<PERSON><PERSON>", "logout": "Logout", "logout_title": "Logout？", "logout_text": "Do you want to log out of the web page?", "invite": "Request invitation code", "invite_text": "Accounts with level 200 can get an invitation code every 45 days<br>Are you sure？", "sos": "Kick out the game", "sos_title": "Logout of the game", "sos_text": "Confirm logout Game？", "register": "Register", "download": "Download", "ranking": "Ranking", "vote": "Vote", "notice": "Notice", "shop": "Shop", "database": "Database", "playground": "Playground", "password": "<label for=\"password\" style=\"display:block;\">Password<div class=\"pull-right\"><a href=\"/forgot\">forgot?</a></div></label>"}, "Vote": {"title": "Vote for MagicMS", "introduce1": "Vote for MagicMS daily on Gtop100 to receive 10,000 NX!", "introduce2": "Voting daily grants +500 NX for each consecutive day of voting, Up to 20,000 coupons", "introduce3": "You will not get your NX unless you complete the entire voting process on Gtop100!", "introduce4": "Fill in your Account Name below and click the button to be redirected to Gtop100.", "introduce5": "On Gtop100, complete the Verification CAPTCHA.", "introduce6": "Click the big red button that says \"Vote for MagicMS\"", "introduce7": "After completing the vote, it can take up to 5 minutes to receive your NX.", "input_error": "No account name", "vote_btn": "Vote on Gtop100 »", "account": "Account Name"}, "Register": {"title": "Registration", "tos_title": "Terms of Service", "tos_message": "By clicking Register, you indicate that you have read and agree to our ", "tos_read": "Please click to view our terms of service", "email_verify": "Please enter the correct email address", "valid_email": "Invalid email", "register_stop": "MagicMS has closed registration on August 1, 2022", "user_length": "Account length must be 5-12 digits", "pwd_diff": "The two passwords entered are inconsistent", "pwd_length": "Password length must be 6-16 digits", "password_verify": "Password contains prohibited characters", "user_verify": "Username must be a combination of letters and numbers only", "captcha_verify": "Failed man-machine authentication", "birthday_verify": "Please enter date of birth", "emailCode_verify": "Please enter email verification code", "invitation_verify": "Please enter invitation code", "register_success": "registration success", "username": "Username", "password": "Password", "password_2": "Confirm Password", "invitationCode": "Invitation Code", "invitationCode2": "Not required", "invitationCode3": "What's this?", "invitationCode4": "required", "email": "Email", "get_email_code": "Get Code", "email_code": "Email Code", "birthday": "Birthday", "captcha": "<PERSON><PERSON>a", "register": "Register »"}, "Notice": {"title": "Notice Board"}, "Library": {"title": "Database", "select_one_category": "Choose at least one category", "please_entire_item": "Please enter the name or ID of the item to be queried", "no_math_data": "No matching data", "thead1": "Icon", "thead2": "Id", "thead3": "Name", "thead4": "Desc", "thead5": "Other", "attribute": "Attribute", "source": "Source", "search": "Search", "total": "A total of ", "page": " pages", "record": "records", "reqLevel": "reqLevel", "reqSTR": "reqSTR", "reqDEX": "reqDEX", "reqINT": "reqINT", "reqLUK": "reqLUK", "reqPOP": "reqPOP", "beginner": "<PERSON><PERSON><PERSON>", "warrior": "Warrior", "magician": "Magician", "bowman": "<PERSON>", "thief": "<PERSON>hief", "pirate": "Pirate", "maxLevel": "Max Level", "itemCategory": "Item Category", "attackSpeed": "Attack Speed", "weapon2HSword": "Two-Handed Sword", "weapon1HSword": "One-Handed Sword", "weaponArm": "Polearm", "weaponBow": "Bow", "weaponCBow": "<PERSON>", "weaponGlove": "Glove", "weaponKnuckle": "<PERSON><PERSON><PERSON>", "weaponGun": "Gun", "weaponSpear": "Spear", "weapon1Haxe": "One-Handed Axe", "weapon2Haxe": "Two-Handed Axe", "weapon1HBlunt": "One-Handed <PERSON>", "weapon2HBlunt": "Two-Handed <PERSON>", "weaponDagger": "<PERSON>gger", "weaponWand": "<PERSON>d", "weaponStaff": "Staff", "attackSpeed2": "Extremely Fast", "attackSpeed3": "Very Fast (3)", "attackSpeed4": "Fast (4)", "attackSpeed5": "Fast (5)", "attackSpeed6": "Normal (6)", "attackSpeed7": "Slow (7)", "attackSpeed8": "Slow (8)", "attackSpeed9": "Slow (9)", "attackSpeed15": "Very Slow", "incSTR": "str", "incDEX": "dex", "incINT": "int", "incLUK": "luk", "incMHP": "hp", "incMMP": "mp", "incPAD": "pad", "incMAD": "mad", "incPDD": "pdd", "incMDD": "mdd", "incACC": "acc", "incEVA": "eva", "incSpeed": "speed", "incJump": "jump", "tuc": "tuc", "baseDropRate": "base drop rate", "mesoText": "meso", "pitchText": "pitch", "nxText": "nx", "gainRate": "rate", "goodPrice": "price", "wildDrop": "Wild Drop", "npcShop": "Npc Shop", "h5Shop": "Web Shop", "gameShop": "Game Shop", "dayCheckin": "Web Checkin", "hireMerchant": "<PERSON><PERSON>", "respawnTime": "Respawn Time", "dropItemList": "Drop Item List", "spawnMap": "Spawning Areas", "respawnPeriod": "Respawn Period", "respawnMap": "Map", "relateQuest": "Quest"}, "CashShop": {"title": "Cash Shop", "search_info": "Name or description", "help": "Guide", "help_title": "Cash Shop use help", "category_all": "All", "buy_success": "Successful purchase", "buy_failure": "Failed purchase", "buy_text1": "Are you sure send to ", "buy_text2": "?", "gift_success": "<PERSON><PERSON> successfully", "gift_failure": "Failed to send", "receiver_name": "Receiving name", "receiver_info": "Please enter the name of the receiving character", "birthday_info": "Please enter the date of birth", "aging": "Valid period", "price": "price", "shop_name": "product name", "gift_confirm_title": "Are you sure to send to the {0} character?", "gift_post_failure": "The information is not complete, the order failed!", "transport": "MagicMS Logistics Center", "stock": "Stock", "sale_time_before": "The product is not yet on sale</br>Sale time： {0}", "sale_time_after": "Cannot be purchased after the sale time", "not_allow_gift": "Goods cannot be given away", "not_stock": "Insufficient product inventory", "not_allow_buy": "Can't buy the product", "buy_confirm": "Commodity: {0}</br>Valid period: {3}</br>Price: <font color=\"#ff8c00\"><b>{1} </b></font><font color=\"#18bc9c\">{2}</font></br>Once confirmed, it will not be returned or exchanged. Are you sure?", "select-char": "Select the receiving character", "cs_count_1": "A total of ", "cs_count_2": " products "}, "Playground": {"title": "MagicMS Playground", "slogan": "Make MapleStory Great Again!", "noAccess": "You do not have permission to access this page, Please login first", "nightlyTip": "You are accessing a version built from nightly code, and it is likely to be unstable."}}