<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API修复验证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>API修复验证测试</h1>
        <p>测试响应拦截器修复后，各个API是否正常工作</p>

        <div class="test-section">
            <h3>1. 服务器状态API测试</h3>
            <button class="test-button" onclick="testServerStatus()">测试服务器状态</button>
            <div id="server-status-result"></div>
        </div>

        <div class="test-section">
            <h3>2. 角色排行榜API测试</h3>
            <button class="test-button" onclick="testCharacterRanking()">测试角色排行榜</button>
            <div id="character-ranking-result"></div>
        </div>

        <div class="test-section">
            <h3>3. 公告列表API测试</h3>
            <button class="test-button" onclick="testNoticeList()">测试公告列表</button>
            <div id="notice-list-result"></div>
        </div>

        <div class="test-section">
            <h3>4. TOS API测试（动态内容）</h3>
            <button class="test-button" onclick="testTOSAPI()">测试TOS API</button>
            <div id="tos-result"></div>
        </div>

        <div class="test-section">
            <h3>5. 综合测试</h3>
            <button class="test-button" onclick="runAllTests()">运行所有测试</button>
            <div id="all-tests-result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080';

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = message;
        }

        async function testServerStatus() {
            const resultId = 'server-status-result';
            showResult(resultId, '正在测试服务器状态API...', 'info');

            try {
                const response = await fetch(`${API_BASE}/api/v1/game/status`);
                const data = await response.json();

                if (response.ok && (data.code === 200 || data.code === 0)) {
                    showResult(resultId, `✅ 服务器状态API正常！\n状态: ${data.data.status}\n在线人数: ${data.data.count}\n响应码: ${data.code}\n\n完整响应:\n${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult(resultId, `❌ 服务器状态API失败: ${data.message || '未知错误'}\n响应码: ${data.code}\n\n完整响应:\n${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult(resultId, `❌ 服务器状态API网络错误: ${error.message}`, 'error');
            }
        }

        async function testCharacterRanking() {
            const resultId = 'character-ranking-result';
            showResult(resultId, '正在测试角色排行榜API...', 'info');

            try {
                const response = await fetch(`${API_BASE}/api/v1/game/character/rank`);
                const data = await response.json();

                if (response.ok && (data.code === 200 || data.code === 0)) {
                    const characters = Array.isArray(data.data) ? data.data : [];
                    showResult(resultId, `✅ 角色排行榜API正常！\n角色数量: ${characters.length}\n响应码: ${data.code}\n\n前3名角色:\n${characters.slice(0, 3).map(c => `${c.name || c.character_name} (等级: ${c.level})`).join('\n')}`, 'success');
                } else {
                    showResult(resultId, `❌ 角色排行榜API失败: ${data.message || '未知错误'}\n响应码: ${data.code}\n\n完整响应:\n${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult(resultId, `❌ 角色排行榜API网络错误: ${error.message}`, 'error');
            }
        }

        async function testNoticeList() {
            const resultId = 'notice-list-result';
            showResult(resultId, '正在测试公告列表API...', 'info');

            try {
                const response = await fetch(`${API_BASE}/api/v1/notice/list?size=5&page=1`);
                const data = await response.json();

                if (response.ok && (data.code === 200 || data.code === 0)) {
                    const notices = data.data.items || [];
                    showResult(resultId, `✅ 公告列表API正常！\n公告数量: ${notices.length}\n总数: ${data.data.total}\n响应码: ${data.code}\n\n最新公告:\n${notices.slice(0, 3).map(n => `${n.title} (${n.create_time})`).join('\n')}`, 'success');
                } else {
                    showResult(resultId, `❌ 公告列表API失败: ${data.message || '未知错误'}\n响应码: ${data.code}\n\n完整响应:\n${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult(resultId, `❌ 公告列表API网络错误: ${error.message}`, 'error');
            }
        }

        async function testTOSAPI() {
            const resultId = 'tos-result';
            showResult(resultId, '正在测试TOS API...', 'info');

            try {
                const response = await fetch(`${API_BASE}/api/tos`);
                const data = await response.json();

                if (response.ok && (data.code === 200 || data.code === 0)) {
                    const tosData = data.data;
                    showResult(resultId, `✅ TOS API正常！\n标题: ${tosData.title}\n创建时间: ${tosData.create_time}\n内容长度: ${tosData.content ? tosData.content.length : 0} 字符\n响应码: ${data.code}\n\n内容预览:\n${tosData.content ? tosData.content.substring(0, 200) + '...' : '无内容'}`, 'success');
                } else {
                    showResult(resultId, `❌ TOS API失败: ${data.message || '未知错误'}\n响应码: ${data.code}\n\n完整响应:\n${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult(resultId, `❌ TOS API网络错误: ${error.message}`, 'error');
            }
        }

        async function runAllTests() {
            const resultId = 'all-tests-result';
            showResult(resultId, '正在运行所有测试...', 'info');

            const tests = [
                { name: '服务器状态', fn: testServerStatus },
                { name: '角色排行榜', fn: testCharacterRanking },
                { name: '公告列表', fn: testNoticeList },
                { name: 'TOS API', fn: testTOSAPI }
            ];

            let results = [];
            for (const test of tests) {
                try {
                    await test.fn();
                    results.push(`✅ ${test.name}: 通过`);
                } catch (error) {
                    results.push(`❌ ${test.name}: 失败 - ${error.message}`);
                }
                // 等待一下避免请求过快
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            showResult(resultId, `测试完成！\n\n${results.join('\n')}\n\n🎉 所有关键API都应该正常工作了！`, 'success');
        }
    </script>
</body>
</html>
