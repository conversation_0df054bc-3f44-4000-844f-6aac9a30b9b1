# MagicMS 现代化 Web 应用

基于 React 19 + TypeScript + Vite 的 MapleStory 私服管理系统现代化重构项目。

## 🚀 技术栈

- **前端框架**: React 19 + TypeScript
- **构建工具**: Vite 5.5.0
- **样式方案**: Tailwind CSS + Shadcn UI
- **路由管理**: React Router v6
- **状态管理**: React Context + Hooks
- **国际化**: i18next
- **HTTP 客户端**: Axios
- **图标库**: Lucide React
- **UI 组件**: Radix UI 原语

## 📦 项目结构

```
src/
├── api/                    # API 服务层
│   ├── client.ts          # Axios 客户端配置
│   ├── services/          # 业务 API 服务
│   └── types.ts           # API 类型定义
├── components/            # 组件库
│   ├── ui/               # 基础 UI 组件
│   ├── common/           # 通用组件
│   ├── layout/           # 布局组件
│   └── game/             # 游戏专用组件
├── pages/                # 页面组件
├── lib/                  # 工具库
│   ├── router.tsx        # 路由配置
│   ├── utils.ts          # 工具函数
│   └── i18n.ts           # 国际化配置
├── types/                # 类型定义
└── styles/               # 样式文件
```

## 🎯 功能特性

### 已实现功能
- ✅ 现代化项目架构
- ✅ 完整的组件库系统（20+ 组件）
- ✅ 响应式布局设计
- ✅ 多语言支持（中文/英文）
- ✅ 路由懒加载
- ✅ API 层封装
- ✅ 类型安全保障
- ✅ 组件展示页面

### 核心组件
- **基础组件**: Button, Input, Card, Badge, Alert, Avatar
- **高级组件**: Table, Tabs, Select, Dialog, Pagination
- **表单组件**: Form, Textarea, Checkbox
- **游戏组件**: ServerStatus, CharacterCard, RankingTable

## 🛠️ 开发指南

### 环境要求
- Node.js >= 18
- npm >= 9

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:5173

### 构建生产版本
```bash
npm run build
```

### 预览生产构建
```bash
npm run preview
```

## 📱 页面路由

- `/` - 首页
- `/download` - 下载页面
- `/ranking` - 排行榜
- `/shop` - 商城
- `/library` - 资料库
- `/vote` - 投票
- `/notice` - 公告
- `/auth/login` - 登录
- `/auth/register` - 注册
- `/profile` - 个人资料
- `/demo` - 组件展示

## 🎨 组件库

详细的组件文档请查看 [COMPONENT_LIBRARY.md](./COMPONENT_LIBRARY.md)

### 快速使用
```tsx
import { Button, Card, CardContent, CardHeader, CardTitle } from './components/ui';

function MyComponent() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>示例卡片</CardTitle>
      </CardHeader>
      <CardContent>
        <Button variant="primary">点击按钮</Button>
      </CardContent>
    </Card>
  );
}
```

## 🌐 国际化

支持中文和英文两种语言，使用 i18next 实现：

```tsx
import { useTranslation } from 'react-i18next';

function MyComponent() {
  const { t } = useTranslation();
  return <h1>{t('welcome.title')}</h1>;
}
```

## 📋 项目进度

### 已完成
- [x] **Task 1 - 项目初始化**: 创建现代化项目结构
- [x] **Task 2 - 核心架构设计**: 实现路由、状态管理、API 层
- [x] **Task 3 - 通用组件库开发**: 完成 20+ 个高质量 UI 组件
  - ✅ 修复所有导入导出错误
  - ✅ 解决 Tailwind CSS v4 兼容性问题
  - ✅ 降级到稳定的 Tailwind CSS v3.4.0
  - ✅ 修复 PostCSS 配置
  - ✅ 应用样式正常显示

- ✅ **Task 4 - 国际化(i18n)系统重构**: 优化多语言支持
  - ✅ 创建模块化翻译文件结构 (`src/locales/zh/common.json`, `src/locales/en/common.json`)
  - ✅ 更新i18n配置支持命名空间 (`src/i18n.ts`)
  - ✅ 创建语言切换组件 (`src/components/common/LanguageSwitcher.tsx`)
  - ✅ 更新现有组件使用新的翻译键格式 (Header组件使用 `common:nav.home` 格式)
  - ✅ 测试语言切换功能 (自定义下拉菜单实现，支持点击外部关闭)

- ✅ **Task 5 - 首页现代化重构**: 重构主页面，实现现代化设计和响应式布局
  - ✅ 创建模块化首页组件架构 (`src/components/home/<USER>
    - `HeroSection`: 现代化英雄区域，包含渐变背景、动画效果、统计数据展示
    - `ServerInfoSection`: 服务器信息展示，带有图标和悬停效果
    - `FeaturesSection`: 功能特色卡片，响应式网格布局
    - `GameFeaturesSection`: 游戏特色详细展示
    - `RankingsSection`: 排行榜预览区域
  - ✅ 完善首页翻译内容，支持中英文切换 (`common:home.*`)
  - ✅ 实现现代化设计风格 (渐变背景、动画效果、卡片悬停、响应式布局)
  - ✅ 重构HomePage组件使用新的模块化架构
  - ✅ 测试首页功能和样式正常工作

- ✅ **Task 6 - 下载页面重构**: 重构下载页面，优化用户体验和现代化设计
  - ✅ 创建模块化下载页面组件架构 (`src/components/download/`)
    - `DownloadHero`: 现代化下载页面英雄区域
    - `ClientDownloadSection`: 客户端下载区域，支持多版本下载
    - `SystemRequirements`: 系统要求展示组件
    - `DownloadGuide`: 下载指南和安装说明
  - ✅ 完善下载页面翻译内容，支持中英文切换 (`common:download.*`)
  - ✅ 实现现代化设计风格和响应式布局
  - ✅ 修复关键问题：服务器状态API重复请求、下载链接错误、翻译键缺失、样式问题
  - ✅ 实现单例模式ServerStatusManager防止重复API请求
  - ✅ 测试下载页面功能和样式正常工作

- ✅ **Task 7 - 排行榜页面重构**: 重构排行榜页面，实现数据展示和筛选功能
  - ✅ 创建模块化排行榜组件架构 (`src/components/ranking/`)
    - `RankingIconFilters`: 图标式筛选器，替代下拉框，更直观的职业和排序筛选
    - `CharacterRankingTable`: 角色排行榜表格，支持头像显示和字段映射修正
    - `GuildRankingTable`: 公会排行榜表格
    - `RankingPagination`: 分页控制组件
    - `JobIcon`: 职业图标组件，使用真实MapleStory图标
    - `RankIcon`: 排名图标组件
  - ✅ 完善排行榜翻译内容，支持中英文切换 (`common:ranking.*`)
  - ✅ 修正API字段映射问题 (`quest_count`, `monster_book` 等字段名)
  - ✅ 实现角色头像显示 (`/api/avatar/{character_id}`)
  - ✅ 复制并使用旧代码中的职业图标资源 (`/static/images/*.png`)
  - ✅ 替换emoji图标为真实MapleStory职业图标
  - ✅ 实现图标式筛选UI，提升用户体验
  - ✅ 修正公会信息显示的字段映射
  - ✅ 测试排行榜页面功能和样式正常工作

- [✅] **Task 10 - 投票页面重构**: 重构投票页面，实现现代化投票系统
  - ✅ 创建了现代化的投票页面UI设计 (VoteHero, VoteRewards, VoteInstructions, VoteForm)
  - ✅ 实现了投票奖励展示组件，清晰展示每日奖励和连续投票奖励
  - ✅ 添加了详细的投票说明和注意事项，提升用户体验
  - ✅ 实现了投票表单组件与API集成 (`/api/v1/vote/`)
  - ✅ 添加了完整的中英文翻译支持 (`common:vote.*`)
  - ✅ 集成了外部投票系统跳转功能 (Gtop100)
  - ✅ 实现了表单验证和错误处理
  - ✅ 采用现代化响应式设计，支持移动端

- [✅] **Task 11 - 公告页面重构**: 重构公告页面，实现现代化公告系统
  - ✅ 创建了现代化的公告列表页面 (NoticePage + NoticeHero + NoticeList)
  - ✅ 实现了公告详情页面 (NoticeDetailPage + NoticeDetail)
  - ✅ 添加了公告列表组件支持分页加载和无限滚动
  - ✅ 实现了公告详情组件支持Markdown内容渲染
  - ✅ 添加了公告分类标识和状态徽章 (更新/活动/维护/重要)
  - ✅ 集成了完整的中英文翻译支持 (`common:notice.*`)
  - ✅ 实现了响应式设计和加载状态处理
  - ✅ 支持公告详情页面的参数路由 (`/notice/:id`)
  - ✅ 创建了专用的新闻API服务 (`newsService`)

### 进行中
- 暂无进行中的任务

### 待完成
- [ ] **Task 8 - 商城页面重构**: 重构商城页面
- [ ] **Task 9 - 资料库页面重构**: 重构资料库页面
- [ ] **Task 12 - 测试与优化**: 全面测试和性能优化

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🔗 相关链接

- [组件库文档](./COMPONENT_LIBRARY.md)
- [Vite 文档](https://vitejs.dev/)
- [React 文档](https://react.dev/)
- [Tailwind CSS](https://tailwindcss.com/)
- [Shadcn UI](https://ui.shadcn.com/)
