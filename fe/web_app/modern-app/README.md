# MagicMS 现代化 Web 应用

基于 React 19 + TypeScript + Vite 的 MapleStory 私服管理系统现代化重构项目。

## 🚀 技术栈

- **前端框架**: React 19 + TypeScript
- **构建工具**: Vite 5.5.0
- **样式方案**: Tailwind CSS + Shadcn UI
- **路由管理**: React Router v6
- **状态管理**: React Context + Hooks
- **国际化**: i18next
- **HTTP 客户端**: Axios
- **图标库**: Lucide React
- **UI 组件**: Radix UI 原语

## 📦 项目结构

```
src/
├── api/                    # API 服务层
│   ├── client.ts          # Axios 客户端配置
│   ├── services/          # 业务 API 服务
│   └── types.ts           # API 类型定义
├── components/            # 组件库
│   ├── ui/               # 基础 UI 组件
│   ├── common/           # 通用组件
│   ├── layout/           # 布局组件
│   └── game/             # 游戏专用组件
├── pages/                # 页面组件
├── lib/                  # 工具库
│   ├── router.tsx        # 路由配置
│   ├── utils.ts          # 工具函数
│   └── i18n.ts           # 国际化配置
├── types/                # 类型定义
└── styles/               # 样式文件
```

## 🎯 功能特性

### 已实现功能
- ✅ 现代化项目架构
- ✅ 完整的组件库系统（20+ 组件）
- ✅ 响应式布局设计
- ✅ 多语言支持（中文/英文）
- ✅ 路由懒加载
- ✅ API 层封装
- ✅ 类型安全保障
- ✅ 组件展示页面

### 核心组件
- **基础组件**: Button, Input, Card, Badge, Alert, Avatar
- **高级组件**: Table, Tabs, Select, Dialog, Pagination
- **表单组件**: Form, Textarea, Checkbox
- **游戏组件**: ServerStatus, CharacterCard, RankingTable

## 🛠️ 开发指南

### 环境要求
- Node.js >= 18
- npm >= 9

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:5173

### 构建生产版本
```bash
npm run build
```

### 预览生产构建
```bash
npm run preview
```

## 📱 页面路由

- `/` - 首页
- `/download` - 下载页面
- `/ranking` - 排行榜
- `/shop` - 商城
- `/library` - 资料库
- `/vote` - 投票
- `/notice` - 公告
- `/auth/login` - 登录
- `/auth/register` - 注册
- `/profile` - 个人资料
- `/demo` - 组件展示

## 🎨 组件库

详细的组件文档请查看 [COMPONENT_LIBRARY.md](./COMPONENT_LIBRARY.md)

### 快速使用
```tsx
import { Button, Card, CardContent, CardHeader, CardTitle } from './components/ui';

function MyComponent() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>示例卡片</CardTitle>
      </CardHeader>
      <CardContent>
        <Button variant="primary">点击按钮</Button>
      </CardContent>
    </Card>
  );
}
```

## 🌐 国际化

支持中文和英文两种语言，使用 i18next 实现：

```tsx
import { useTranslation } from 'react-i18next';

function MyComponent() {
  const { t } = useTranslation();
  return <h1>{t('welcome.title')}</h1>;
}
```

## 📋 项目进度

### 已完成
- [x] **Task 1 - 项目初始化**: 创建现代化项目结构
- [x] **Task 2 - 核心架构设计**: 实现路由、状态管理、API 层
- [x] **Task 3 - 通用组件库开发**: 完成 20+ 个高质量 UI 组件

### 进行中
- [ ] **Task 4 - 国际化(i18n)系统重构**: 优化多语言支持

### 待完成
- [ ] **Task 5 - 首页现代化重构**: 重构主页面
- [ ] **Task 6 - 下载页面重构**: 重构下载页面
- [ ] **Task 7 - 排行榜页面重构**: 重构排行榜页面
- [ ] **Task 8 - 商城页面重构**: 重构商城页面
- [ ] **Task 9 - 资料库页面重构**: 重构资料库页面
- [ ] **Task 10 - 投票页面重构**: 重构投票页面
- [ ] **Task 11 - 公告页面重构**: 重构公告页面
- [ ] **Task 12 - 测试与优化**: 全面测试和性能优化

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🔗 相关链接

- [组件库文档](./COMPONENT_LIBRARY.md)
- [Vite 文档](https://vitejs.dev/)
- [React 文档](https://react.dev/)
- [Tailwind CSS](https://tailwindcss.com/)
- [Shadcn UI](https://ui.shadcn.com/)
