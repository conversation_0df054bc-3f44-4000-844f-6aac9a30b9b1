{"name": "modern-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.5.3", "react-markdown": "^10.1.0", "react-router-dom": "^6.30.1", "rehype-raw": "^7.0.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/js": "^9.8.0", "@testing-library/jest-dom": "^6.6.3", "@types/jest": "^30.0.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.21", "eslint": "^9.8.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.3", "jest-environment-jsdom": "^30.0.2", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "ts-jest": "^29.4.0", "typescript": "^5.5.3", "typescript-eslint": "^8.0.0", "vite": "^5.4.0"}, "overrides": {"eslint": "$eslint"}}