// 基础类型定义
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  code?: number;
}

// 用户相关类型
export interface User {
  id: number;
  username: string;
  email?: string;
  avatar?: string;
  level?: number;
  exp?: number;
  lastLogin?: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  refresh_token: string;
  expires_in: number;
  refresh_expires_in: number;
  user: User;
}

// 游戏相关类型
export interface Character {
  id: number;
  name: string;
  level: number;
  job: string;
  exp: number;
  fame: number;
  guildName?: string;
  lastLogin?: string;
}

export interface ServerStatus {
  status: 'online' | 'offline' | 'maintenance';
  onlineCount: number;
  serverTime: string;
  version: string;
  rates: {
    exp: number;
    meso: number;
    drop: number;
    boss: number;
    quest: number;
  };
}

// 排行榜类型
export interface RankingItem {
  rank: number;
  character: Character;
  value: number;
  type: 'level' | 'fame' | 'quest' | 'monsterbook';
}

export interface RankingFilter {
  job?: 'all' | 'beginner' | 'warrior' | 'magician' | 'bowman' | 'thief' | 'pirate' | 'cygnus' | 'aran';
  sort?: 'level' | 'fame' | 'quest' | 'monsterbook' | 'guild';
  page?: number;
  limit?: number;
}

// 商城类型
export interface ShopItem {
  id: number;
  itemId: number;
  name: string;
  description?: string;
  price: number;
  category: string;
  icon?: string;
  quantity?: number;
  isLimited?: boolean;
  endDate?: string;
}

export interface ShopCategory {
  id: string;
  name: string;
  description?: string;
}

// 资料库类型
export interface LibraryItem {
  id: number;
  name: string;
  description?: string;
  category: string;
  subcategory?: string;
  icon?: string;
  stats?: Record<string, number>;
  requirements?: {
    level?: number;
    str?: number;
    dex?: number;
    int?: number;
    luk?: number;
  };
  rarity?: number;
  source?: string[];
}

export interface LibraryFilter {
  category?: string;
  subcategory?: string;
  keyword?: string;
  minLevel?: number;
  maxLevel?: number;
  page?: number;
  limit?: number;
}

// 公告类型
export interface Notice {
  id: number;
  title: string;
  content: string;
  author: string;
  createdAt: string;
  updatedAt?: string;
  isTop?: boolean;
  category?: string;
  viewCount?: number;
}

// 投票类型
export interface VoteOption {
  id: number;
  text: string;
  votes: number;
}

export interface Vote {
  id: number;
  title: string;
  description?: string;
  options: VoteOption[];
  totalVotes: number;
  endDate?: string;
  isActive: boolean;
  allowMultiple?: boolean;
}

// 通用分页类型
export interface PaginationParams {
  page: number;
  limit: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 语言类型
export type Language = 'zh' | 'en';

// 主题类型
export type Theme = 'light' | 'dark' | 'system';
