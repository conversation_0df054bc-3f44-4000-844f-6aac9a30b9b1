// 基础类型定义 - 根据OpenAPI规范更新
export interface ApiResponse<T = any> {
  response_id: string;
  code: number; // 0表示成功
  message: string;
  data: T;
}

// 用户相关类型
export interface User {
  id: number;
  username: string;
  email?: string;
  avatar?: string;
  level?: number;
  exp?: number;
  lastLogin?: string;
}

// 服务条款类型
export interface TermsOfService {
  title: string;
  create_time: string;
  content: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  refresh_token: string;
  expires_in: number;
  refresh_expires_in: number;
  user: User;
}

export interface RegisterRequest {
  username: string;
  password: string;
  confirmPassword: string;
  email: string;
  emailCode: string;
  birthday: string;
  invitationCode?: string;
  captcha?: string;
}

// 用户详细信息类型（基于API规范）
export interface UserInfo {
  id: number;
  name: string;
  nick?: string | null;
  email?: string | null;
  logged_in: number;
  last_login?: string | null;
  nx: number;
  mp: number;
  np: number;
  slots: number;
  gender: number;
  banned: number;
  banned_reason?: string | null;
  web_admin?: number | null;
  reward_point: number;
  vote_points: number;
  language: number;
  create_at: string;
}

// 游戏相关类型
export interface Character {
  id: number;
  name: string;
  level: number;
  job: string;
  exp: number;
  fame: number;
  guildName?: string;
  lastLogin?: string;
}

// 角色详细信息类型（基于API规范）
export interface CharInfo {
  id: number;
  account_id: number;
  name: string;
  level: number;
  str_: number;
  dex: number;
  int_: number;
  luk: number;
  max_hp: number;
  max_mp: number;
  gender: number;
  map: number;
  rank: number;
  meso: number;
  job: number;
  fame: number;
  hair: number;
  face: number;
  skin_color: number;
  guild_id: number;
  create_date: string;
  last_logout_time: string;
  jail_expire: number;
}

// 角色列表响应类型
export interface CharListResponse {
  items: CharInfo[];
}

// 签到请求类型
export interface CheckInRequest {
  character_id: number;
}

// 背包道具类型（基于API规范）
export interface InvItemModel {
  id: number; // inventoryitemid
  type: number;
  character_id: number; // characterid
  item_id: number; // itemid
  inventory_type: number; // inventorytype
  position: number;
  quantity: number;
  owner: string;
  pet_id: number; // petid
  flag: number;
  expiration: number;
  gift_from: string; // giftFrom
}

// 角色背包响应类型
export interface CharItemResponse {
  items: InvItemModel[];
}

// 装备道具类型（基于游戏逻辑扩展）
export interface EquipItemModel extends InvItemModel {
  // 装备特有属性（前端预留，后端可能暂未支持）
  upgrade_slots?: number; // 升级次数
  level?: number; // 装备等级
  str?: number; // 力量
  dex?: number; // 敏捷
  int?: number; // 智力
  luk?: number; // 运气
  hp?: number; // 生命值
  mp?: number; // 魔法值
  watk?: number; // 物理攻击力
  matk?: number; // 魔法攻击力
  wdef?: number; // 物理防御力
  mdef?: number; // 魔法防御力
  acc?: number; // 命中率
  avoid?: number; // 回避率
  hands?: number; // 手技
  speed?: number; // 速度
  jump?: number; // 跳跃力
}

// 角色装备响应类型
export interface CharEquipResponse {
  items: EquipItemModel[];
}

export interface ServerStatus {
  isOnline: boolean;
  status: string; // '正常' | '异常' | 'EOL' | etc
  onlinePlayers: number;
  count: number; // 在线玩家数量（兼容旧API）
  serverTime?: string;
  version?: string;
  invite: boolean; // 是否启用邀请码注册
  expRate: number;
  mesoRate: number;
  dropRate: number;
  bossRate: number;
  rates?: {
    exp: number;
    meso: number;
    drop: number;
    boss: number;
    quest: number;
  };
}

// 排行榜类型
export interface RankingItem {
  id: number;
  rank: number;
  name: string;
  job: number;
  jobName: string;
  level: number;
  exp?: number;
  fame: number;
  quest_count: number;  // 修正字段名，匹配API
  monster_book: number; // 修正字段名，匹配API
  avatar?: string;
  guild?: {
    guildid: number;     // 修正字段名，匹配API
    name: string;
    logo: number | null;
    logoColor: number;   // 修正字段名，匹配API
    logoBG: number | null; // 修正字段名，匹配API
    logoBGColor: number; // 修正字段名，匹配API
    alliance_name?: string | null; // 修正字段名，匹配API
  };
  value: number; // 当前排序字段的值
  type: 'level' | 'fame' | 'quest' | 'monsterbook';
}

export interface GuildRankingItem {
  guildid: number;     // 修正字段名，匹配API
  rank: number;
  name: string;
  leader_name: string; // 修正字段名，匹配API
  leader: number;      // 会长ID，匹配API
  member: number;      // 修正字段名，匹配API
  capacity: number;    // 修正字段名，匹配API
  GP: number;          // 修正字段名，匹配API
  logo: number | null;
  logoColor: number;   // 修正字段名，匹配API
  logoBG: number | null; // 修正字段名，匹配API
  logoBGColor: number; // 修正字段名，匹配API
  alliance_name?: string | null; // 修正字段名，匹配API
  pub_notice?: string | null;    // 修正字段名，匹配API
  value: number; // 积分或其他排序值
}

export interface RankingFilter {
  job?: 'all' | 'beginner' | 'warrior' | 'magician' | 'bowman' | 'thief' | 'pirate' | 'cygnus' | 'aran';
  sort?: 'level' | 'fame' | 'quest' | 'monsterbook' | 'guild';
  page?: number;
  limit?: number;
}

export interface RankingResponse {
  items: RankingItem[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface GuildRankingResponse {
  items: GuildRankingItem[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 商城类型
export interface ShopItem {
  id: number;
  itemId: number;
  name: string;
  description?: string;
  price: number;
  category: string;
  icon?: string;
  quantity?: number;
  isLimited?: boolean;
  endDate?: string;
}

export interface ShopCategory {
  id: string;
  name: string;
  description?: string;
}

// 资料库类型
export interface LibraryItem {
  id: number;
  name: string;
  description?: string;
  category: string;
  subcategory?: string;
  icon?: string;
  stats?: Record<string, number>;
  requirements?: {
    level?: number;
    str?: number;
    dex?: number;
    int?: number;
    luk?: number;
  };
  rarity?: number;
  source?: string[];
}

export interface LibraryFilter {
  category?: string;
  subcategory?: string;
  keyword?: string;
  minLevel?: number;
  maxLevel?: number;
  page?: number;
  limit?: number;
}

// 公告类型
export interface Notice {
  id: number;
  title: string;
  content: string;
  author: string;
  createdAt: string;
  updatedAt?: string;
  isTop?: boolean;
  category?: string;
  viewCount?: number;
}

// 投票类型
export interface VoteOption {
  id: number;
  text: string;
  votes: number;
}

export interface Vote {
  id: number;
  title: string;
  description?: string;
  options: VoteOption[];
  totalVotes: number;
  endDate?: string;
  isActive: boolean;
  allowMultiple?: boolean;
}

// 通用分页类型
export interface PaginationParams {
  page: number;
  limit: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 语言类型
export type Language = 'zh' | 'en';

// 主题类型
export type Theme = 'light' | 'dark' | 'system';
