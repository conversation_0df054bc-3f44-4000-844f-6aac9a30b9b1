// 基础类型定义 - 根据OpenAPI规范更新
export interface ApiResponse<T = any> {
  response_id: string;
  code: number; // 0表示成功
  message: string;
  data: T;
}

// 用户相关类型
export interface User {
  id: number;
  username: string;
  email?: string;
  avatar?: string;
  level?: number;
  exp?: number;
  lastLogin?: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  refresh_token: string;
  expires_in: number;
  refresh_expires_in: number;
  user: User;
}

export interface RegisterRequest {
  username: string;
  password: string;
  confirmPassword: string;
  email: string;
  emailCode: string;
  birthday: string;
  invitationCode?: string;
  captcha?: string;
}

// 游戏相关类型
export interface Character {
  id: number;
  name: string;
  level: number;
  job: string;
  exp: number;
  fame: number;
  guildName?: string;
  lastLogin?: string;
}

export interface ServerStatus {
  isOnline: boolean;
  status: string; // '正常' | '异常' | 'EOL' | etc
  onlinePlayers: number;
  count: number; // 在线玩家数量（兼容旧API）
  serverTime?: string;
  version?: string;
  invite: boolean; // 是否启用邀请码注册
  expRate: number;
  mesoRate: number;
  dropRate: number;
  bossRate: number;
  rates?: {
    exp: number;
    meso: number;
    drop: number;
    boss: number;
    quest: number;
  };
}

// 排行榜类型
export interface RankingItem {
  id: number;
  rank: number;
  name: string;
  job: number;
  jobName: string;
  level: number;
  exp?: number;
  fame: number;
  quest_count: number;  // 修正字段名，匹配API
  monster_book: number; // 修正字段名，匹配API
  avatar?: string;
  guild?: {
    guildid: number;     // 修正字段名，匹配API
    name: string;
    logo: number | null;
    logoColor: number;   // 修正字段名，匹配API
    logoBG: number | null; // 修正字段名，匹配API
    logoBGColor: number; // 修正字段名，匹配API
    alliance_name?: string | null; // 修正字段名，匹配API
  };
  value: number; // 当前排序字段的值
  type: 'level' | 'fame' | 'quest' | 'monsterbook';
}

export interface GuildRankingItem {
  guildid: number;     // 修正字段名，匹配API
  rank: number;
  name: string;
  leader_name: string; // 修正字段名，匹配API
  leader: number;      // 会长ID，匹配API
  member: number;      // 修正字段名，匹配API
  capacity: number;    // 修正字段名，匹配API
  GP: number;          // 修正字段名，匹配API
  logo: number | null;
  logoColor: number;   // 修正字段名，匹配API
  logoBG: number | null; // 修正字段名，匹配API
  logoBGColor: number; // 修正字段名，匹配API
  alliance_name?: string | null; // 修正字段名，匹配API
  pub_notice?: string | null;    // 修正字段名，匹配API
  value: number; // 积分或其他排序值
}

export interface RankingFilter {
  job?: 'all' | 'beginner' | 'warrior' | 'magician' | 'bowman' | 'thief' | 'pirate' | 'cygnus' | 'aran';
  sort?: 'level' | 'fame' | 'quest' | 'monsterbook' | 'guild';
  page?: number;
  limit?: number;
}

export interface RankingResponse {
  items: RankingItem[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface GuildRankingResponse {
  items: GuildRankingItem[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 商城类型
export interface ShopItem {
  id: number;
  itemId: number;
  name: string;
  description?: string;
  price: number;
  category: string;
  icon?: string;
  quantity?: number;
  isLimited?: boolean;
  endDate?: string;
}

export interface ShopCategory {
  id: string;
  name: string;
  description?: string;
}

// 资料库类型
export interface LibraryItem {
  id: number;
  name: string;
  description?: string;
  category: string;
  subcategory?: string;
  icon?: string;
  stats?: Record<string, number>;
  requirements?: {
    level?: number;
    str?: number;
    dex?: number;
    int?: number;
    luk?: number;
  };
  rarity?: number;
  source?: string[];
}

export interface LibraryFilter {
  category?: string;
  subcategory?: string;
  keyword?: string;
  minLevel?: number;
  maxLevel?: number;
  page?: number;
  limit?: number;
}

// 公告类型
export interface Notice {
  id: number;
  title: string;
  content: string;
  author: string;
  createdAt: string;
  updatedAt?: string;
  isTop?: boolean;
  category?: string;
  viewCount?: number;
}

// 投票类型
export interface VoteOption {
  id: number;
  text: string;
  votes: number;
}

export interface Vote {
  id: number;
  title: string;
  description?: string;
  options: VoteOption[];
  totalVotes: number;
  endDate?: string;
  isActive: boolean;
  allowMultiple?: boolean;
}

// 通用分页类型
export interface PaginationParams {
  page: number;
  limit: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 语言类型
export type Language = 'zh' | 'en';

// 主题类型
export type Theme = 'light' | 'dark' | 'system';
