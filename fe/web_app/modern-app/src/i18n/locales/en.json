{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "confirm": "Confirm", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "search": "Search", "reset": "Reset", "submit": "Submit", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "open": "Open", "view": "View", "download": "Download", "upload": "Upload", "refresh": "Refresh", "more": "More", "less": "Less", "all": "All", "none": "None", "yes": "Yes", "no": "No"}, "nav": {"home": "Home", "download": "Download", "ranking": "Ranking", "vote": "Vote", "notice": "Notice", "shop": "Shop", "library": "Library", "register": "Register", "login": "<PERSON><PERSON>", "logout": "Logout", "profile": "Profile"}, "home": {"title": "MagicMS - Magic MapleStory", "welcome": "Welcome to MagicMS!", "description": "A v83 private server with modern game mechanics and unique custom features", "serverInfo": {"title": "Server Information", "status": "Status", "online": "Online Players", "time": "Server Time", "version": "Version", "rates": {"exp": "EXP Rate", "meso": "Meso Rate", "drop": "Drop Rate", "boss": "Boss Rate", "quest": "Quest Rate"}}, "features": {"title": "Game Features", "list": ["Global monster NX blessing chaos drops", "Equipment level system added", "Scroll effects multiplied, chaos scrolls don't reduce stats", "Dark Tide event with double HP and triple monster count", "Free job switching within 4th job branches, max level 255", "Magician exclusive buff: <PERSON>", "Bowman exclusive buff: Helena's Blessing", "Aran exclusive buff: <PERSON><PERSON>'s Faith", "More enhancements for better gaming experience"]}, "notices": {"title": "Latest Notices", "viewAll": "View All"}}, "auth": {"login": {"title": "<PERSON><PERSON>", "username": "Username", "password": "Password", "remember": "Remember me", "submit": "<PERSON><PERSON>", "forgotPassword": "Forgot password?", "noAccount": "Don't have an account?", "register": "Register now"}, "register": {"title": "Register", "username": "Username", "password": "Password", "confirmPassword": "Confirm Password", "email": "Email", "inviteCode": "Invite Code", "submit": "Register", "hasAccount": "Already have an account?", "login": "Login now"}, "forgotPassword": {"title": "Forgot Password", "email": "Email", "submit": "Send Reset Email", "backToLogin": "Back to Login"}}, "ranking": {"title": "Rankings", "character": "Character Rankings", "guild": "Guild Rankings", "filters": {"job": "Job", "sort": "Sort"}, "jobs": {"all": "All", "beginner": "<PERSON><PERSON><PERSON>", "warrior": "Warrior", "magician": "Magician", "bowman": "<PERSON>", "thief": "<PERSON>hief", "pirate": "Pirate", "cygnus": "<PERSON><PERSON><PERSON>", "aran": "<PERSON><PERSON>"}, "sortTypes": {"level": "Level", "fame": "Fame", "quest": "Quest", "monsterbook": "Monster Book"}, "columns": {"rank": "Rank", "character": "Character", "level": "Level", "job": "Job", "guild": "Guild", "value": "Value"}}, "shop": {"title": "Cash Shop", "categories": "Categories", "search": "Search Items", "price": "Price", "buy": "Buy", "gift": "Gift", "cart": "<PERSON><PERSON>", "checkout": "Checkout"}, "library": {"title": "Item Library", "search": "Search Items", "categories": "Categories", "filters": "Filters", "level": "Level", "stats": "Stats", "requirements": "Requirements", "source": "Source"}, "notice": {"title": "Notices", "latest": "Latest Notices", "viewCount": "Views", "author": "Author", "publishTime": "Published"}, "vote": {"title": "Vote", "active": "Active", "ended": "Ended", "totalVotes": "Total Votes", "vote": "Vote", "results": "Results"}, "profile": {"title": "Profile", "characters": "Characters", "settings": "Settings", "language": "Language", "theme": "Theme"}, "errors": {"404": "Page Not Found", "500": "Server Error", "network": "Network Error", "unauthorized": "Unauthorized", "forbidden": "Forbidden"}}