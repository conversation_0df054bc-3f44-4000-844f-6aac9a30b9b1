import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';

// 导入翻译资源
import zhTranslations from './locales/zh.json';
import enTranslations from './locales/en.json';

const resources = {
  zh: {
    translation: zhTranslations,
  },
  en: {
    translation: enTranslations,
  },
};

i18n
  // 加载翻译文件
  .use(Backend)
  // 检测用户语言
  .use(LanguageDetector)
  // 传递给react-i18next
  .use(initReactI18next)
  // 初始化i18next
  .init({
    resources,
    fallbackLng: 'zh',
    debug: import.meta.env.DEV,

    interpolation: {
      escapeValue: false, // React已经默认转义
    },

    detection: {
      // 检测语言的顺序
      order: ['querystring', 'cookie', 'localStorage', 'sessionStorage', 'navigator', 'htmlTag'],
      
      // 查找语言的参数
      lookupQuerystring: 'lng',
      lookupCookie: 'i18next',
      lookupLocalStorage: 'i18nextLng',
      lookupSessionStorage: 'i18nextLng',
      
      // 缓存用户语言
      caches: ['localStorage', 'cookie'],
      excludeCacheFor: ['cimode'],
      
      // cookie选项
      cookieMinutes: 10080, // 7天
      cookieOptions: { 
        path: '/', 
        sameSite: 'strict' 
      },
    },

    // 后端配置
    backend: {
      loadPath: '/locales/{{lng}}/{{ns}}.json',
    },
  });

export default i18n;

// 导出类型安全的翻译函数
export const t = i18n.t.bind(i18n);

// 语言切换函数
export const changeLanguage = (lng: string) => {
  return i18n.changeLanguage(lng);
};

// 获取当前语言
export const getCurrentLanguage = () => {
  return i18n.language;
};

// 支持的语言列表
export const SUPPORTED_LANGUAGES = [
  { code: 'zh', name: '中文', nativeName: '中文' },
  { code: 'en', name: 'English', nativeName: 'English' },
] as const;
