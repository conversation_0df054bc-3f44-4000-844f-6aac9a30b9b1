import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/Avatar';
import { Character } from '../../types';

interface CharacterCardProps {
  character: Character;
  className?: string;
  onClick?: () => void;
}

export const CharacterCard = ({ character, className, onClick }: CharacterCardProps) => {
  const getJobBadgeVariant = (job: string) => {
    const jobColors: Record<string, "default" | "secondary" | "destructive" | "outline" | "success" | "warning" | "info"> = {
      '战士': 'destructive',
      '法师': 'info',
      '弓箭手': 'success',
      '飞侠': 'secondary',
      '海盗': 'warning',
    };
    
    const baseJob = job.split(' ')[0]; // 获取基础职业
    return jobColors[baseJob] || 'default';
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  return (
    <Card 
      className={`cursor-pointer hover:shadow-lg transition-shadow ${className}`}
      onClick={onClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-center space-x-3">
          <Avatar className="h-12 w-12">
            <AvatarImage 
              src={character.avatar || `/avatars/${character.job}.png`} 
              alt={character.name}
            />
            <AvatarFallback>
              {character.name.charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <CardTitle className="text-lg">{character.name}</CardTitle>
            <CardDescription className="flex items-center gap-2">
              <Badge variant={getJobBadgeVariant(character.job)}>
                {character.job}
              </Badge>
              <span>Lv.{character.level}</span>
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-muted-foreground">经验值</p>
            <p className="font-semibold">{formatNumber(character.exp)}</p>
          </div>
          <div>
            <p className="text-muted-foreground">金币</p>
            <p className="font-semibold">{formatNumber(character.meso)}</p>
          </div>
          <div>
            <p className="text-muted-foreground">力量</p>
            <p className="font-semibold">{character.str}</p>
          </div>
          <div>
            <p className="text-muted-foreground">敏捷</p>
            <p className="font-semibold">{character.dex}</p>
          </div>
          <div>
            <p className="text-muted-foreground">智力</p>
            <p className="font-semibold">{character.int}</p>
          </div>
          <div>
            <p className="text-muted-foreground">运气</p>
            <p className="font-semibold">{character.luk}</p>
          </div>
        </div>
        
        {character.guildName && (
          <div className="mt-4 pt-4 border-t">
            <p className="text-sm text-muted-foreground">公会</p>
            <p className="font-semibold">{character.guildName}</p>
          </div>
        )}
        
        <div className="mt-4 pt-4 border-t text-xs text-muted-foreground">
          <p>最后登录: {new Date(character.lastLogin).toLocaleDateString('zh-CN')}</p>
          <p>创建时间: {new Date(character.createTime).toLocaleDateString('zh-CN')}</p>
        </div>
      </CardContent>
    </Card>
  );
};
