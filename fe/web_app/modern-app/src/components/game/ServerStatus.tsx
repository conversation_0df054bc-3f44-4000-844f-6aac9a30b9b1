import { useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card';
import { Badge } from '../ui/Badge';
import LoadingSpinner from '../common/LoadingSpinner';
import { useServerStatus } from '../../hooks/useServerStatus';
import { serverStatusManager } from '../../utils/serverStatusManager';

interface ServerStatusProps {
  className?: string;
}

export const ServerStatus = ({ className }: ServerStatusProps) => {
  const { status, loading, error } = useServerStatus();

  useEffect(() => {
    // 启动自动刷新（30秒间隔）
    serverStatusManager.startAutoRefresh(30000);

    // 组件卸载时停止自动刷新
    return () => {
      serverStatusManager.stopAutoRefresh();
    };
  }, []);

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <LoadingSpinner text="获取服务器状态中..." />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <p className="text-destructive">{error}</p>
        </CardContent>
      </Card>
    );
  }

  if (!status) {
    return null;
  }

  const getStatusBadge = (isOnline: boolean) => {
    return (
      <Badge variant={isOnline ? "success" : "destructive"}>
        {isOnline ? "在线" : "离线"}
      </Badge>
    );
  };

  const getOnlineIndicator = (isOnline: boolean) => {
    return (
      <div className={`w-3 h-3 rounded-full ${isOnline ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`} />
    );
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {getOnlineIndicator(status.isOnline)}
          服务器状态
          {getStatusBadge(status.isOnline)}
        </CardTitle>
        <CardDescription>
          实时服务器状态和在线信息
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-primary">{status.onlinePlayers}</p>
            <p className="text-sm text-muted-foreground">在线玩家</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600">{status.expRate}x</p>
            <p className="text-sm text-muted-foreground">经验倍率</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-yellow-600">{status.mesoRate}x</p>
            <p className="text-sm text-muted-foreground">金币倍率</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600">{status.dropRate}x</p>
            <p className="text-sm text-muted-foreground">掉落倍率</p>
          </div>
        </div>
        
        {status.events && status.events.length > 0 && (
          <div className="mt-4 pt-4 border-t">
            <h4 className="font-semibold mb-2">当前活动</h4>
            <div className="space-y-2">
              {status.events.map((event, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm">{event.name}</span>
                  <Badge variant="info">{event.type}</Badge>
                </div>
              ))}
            </div>
          </div>
        )}
        
        <div className="mt-4 pt-4 border-t text-xs text-muted-foreground">
          <p>服务器时间: {new Date(status.serverTime).toLocaleString('zh-CN')}</p>
          <p>版本: {status.version} | 运行时间: {status.uptime}</p>
        </div>
      </CardContent>
    </Card>
  );
};
