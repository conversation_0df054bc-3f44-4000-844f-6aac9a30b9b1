import { useState, useEffect } from 'react';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '../ui/Table';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/Avatar';
import LoadingSpinner from '../common/LoadingSpinner';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/Tabs';
import { gameService } from '../../api/services/game';
import { RankingItem } from '../../types';
import { Trophy, Medal, Award } from 'lucide-react';

interface RankingTableProps {
  className?: string;
}

export const RankingTable = ({ className }: RankingTableProps) => {
  const [rankings, setRankings] = useState<{
    level: RankingItem[];
    fame: RankingItem[];
    guild: RankingItem[];
  }>({
    level: [],
    fame: [],
    guild: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchRankings = async () => {
      try {
        setLoading(true);
        const [levelResponse, fameResponse, guildResponse] = await Promise.all([
          gameService.getCharacterRanking({ sort: 'level', page: 1, limit: 10 }),
          gameService.getCharacterRanking({ sort: 'fame', page: 1, limit: 10 }),
          gameService.getGuildRanking(1, 10)
        ]);

        setRankings({
          level: levelResponse.items || [],
          fame: fameResponse.items || [],
          guild: guildResponse.items || []
        });
        setError(null);
      } catch (err) {
        setError('获取排行榜数据失败');
        console.error('Failed to fetch rankings:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchRankings();
  }, []);

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Trophy className="h-5 w-5 text-yellow-500" />;
      case 2:
        return <Medal className="h-5 w-5 text-gray-400" />;
      case 3:
        return <Award className="h-5 w-5 text-amber-600" />;
      default:
        return <span className="text-muted-foreground font-semibold">#{rank}</span>;
    }
  };

  const getJobBadgeVariant = (job: string) => {
    const jobColors: Record<string, "default" | "secondary" | "destructive" | "outline" | "success" | "warning" | "info"> = {
      '战士': 'destructive',
      '法师': 'info',
      '弓箭手': 'success',
      '飞侠': 'secondary',
      '海盗': 'warning',
    };
    
    const baseJob = job.split(' ')[0];
    return jobColors[baseJob] || 'default';
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <LoadingSpinner text="加载排行榜中..." />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <p className="text-destructive">{error}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>排行榜</CardTitle>
        <CardDescription>查看服务器顶级玩家和公会</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="level" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="level">等级排行</TabsTrigger>
            <TabsTrigger value="fame">人气排行</TabsTrigger>
            <TabsTrigger value="guild">公会排行</TabsTrigger>
          </TabsList>
          
          <TabsContent value="level" className="mt-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-16">排名</TableHead>
                  <TableHead>角色</TableHead>
                  <TableHead>职业</TableHead>
                  <TableHead>等级</TableHead>
                  <TableHead>经验值</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {rankings.level.map((item, index) => (
                  <TableRow key={item.id}>
                    <TableCell className="flex items-center justify-center">
                      {getRankIcon(index + 1)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={item.avatar} alt={item.name} />
                          <AvatarFallback>
                            {item.name.charAt(0).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <span className="font-medium">{item.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getJobBadgeVariant(item.job || '')}>
                        {item.job}
                      </Badge>
                    </TableCell>
                    <TableCell className="font-semibold">{item.level}</TableCell>
                    <TableCell>{formatNumber(item.value)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TabsContent>
          
          <TabsContent value="fame" className="mt-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-16">排名</TableHead>
                  <TableHead>角色</TableHead>
                  <TableHead>职业</TableHead>
                  <TableHead>等级</TableHead>
                  <TableHead>人气值</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {rankings.fame.map((item, index) => (
                  <TableRow key={item.id}>
                    <TableCell className="flex items-center justify-center">
                      {getRankIcon(index + 1)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={item.avatar} alt={item.name} />
                          <AvatarFallback>
                            {item.name.charAt(0).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <span className="font-medium">{item.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getJobBadgeVariant(item.job || '')}>
                        {item.job}
                      </Badge>
                    </TableCell>
                    <TableCell>{item.level}</TableCell>
                    <TableCell className="font-semibold">{formatNumber(item.value)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TabsContent>
          
          <TabsContent value="guild" className="mt-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-16">排名</TableHead>
                  <TableHead>公会名称</TableHead>
                  <TableHead>会长</TableHead>
                  <TableHead>成员数</TableHead>
                  <TableHead>平均等级</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {rankings.guild.map((item, index) => (
                  <TableRow key={item.id}>
                    <TableCell className="flex items-center justify-center">
                      {getRankIcon(index + 1)}
                    </TableCell>
                    <TableCell className="font-medium">{item.name}</TableCell>
                    <TableCell>{item.leader}</TableCell>
                    <TableCell>{item.memberCount}</TableCell>
                    <TableCell className="font-semibold">{item.value}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
