import { useState, useEffect } from 'react';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '../ui/Table';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/Avatar';
import LoadingSpinner from '../common/LoadingSpinner';

import { gameService } from '../../api/services/game';
import { RankingItem } from '../../types';
import { Trophy, Medal, Award } from 'lucide-react';

// API基础URL配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api';

interface RankingTableProps {
  className?: string;
}

export const RankingTable = ({ className }: RankingTableProps) => {
  const [rankings, setRankings] = useState<RankingItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchRankings = async () => {
      try {
        setLoading(true);
        const levelResponse = await gameService.getCharacterRanking({ sort: 'level', page: 1, limit: 10 });
        console.log('Ranking data received:', levelResponse);
        console.log('First item structure:', levelResponse.items?.[0]);
        setRankings(levelResponse.items || []);
        setError(null);
      } catch (err) {
        setError('获取排行榜数据失败');
        console.error('Failed to fetch rankings:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchRankings();
  }, []);

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Trophy className="h-5 w-5 text-yellow-500" />;
      case 2:
        return <Medal className="h-5 w-5 text-gray-400" />;
      case 3:
        return <Award className="h-5 w-5 text-amber-600" />;
      default:
        return <span className="text-muted-foreground font-semibold">#{rank}</span>;
    }
  };

  const getJobBadgeVariant = (job: string | number) => {
    const jobColors: Record<string, "default" | "secondary" | "destructive" | "outline" | "success" | "warning" | "info"> = {
      '战士': 'destructive',
      '法师': 'info',
      '弓箭手': 'success',
      '飞侠': 'secondary',
      '海盗': 'warning',
    };

    // 如果job是数字，先转换为职业名称
    let jobName: string;
    if (typeof job === 'number') {
      jobName = getJobNameFromId(job);
    } else {
      jobName = job || '';
    }

    const baseJob = jobName.split(' ')[0];
    return jobColors[baseJob] || 'default';
  };

  const getJobNameFromId = (jobId: number): string => {
    if (jobId === 0) return '新手';
    if (jobId >= 100 && jobId < 200) return '战士';
    if (jobId >= 200 && jobId < 300) return '法师';
    if (jobId >= 300 && jobId < 400) return '弓箭手';
    if (jobId >= 400 && jobId < 500) return '飞侠';
    if (jobId >= 500 && jobId < 600) return '海盗';
    if (jobId >= 1000 && jobId < 1600) return '骑士团';
    if (jobId >= 2000) return '战神';
    return '未知';
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <LoadingSpinner text="加载排行榜中..." />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <p className="text-destructive">{error}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>等级排行榜</CardTitle>
        <CardDescription>服务器等级TOP10玩家</CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-16">排名</TableHead>
              <TableHead>角色</TableHead>
              <TableHead>职业</TableHead>
              <TableHead>等级</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {rankings.map((item, index) => (
              <TableRow key={item.id}>
                <TableCell className="flex items-center justify-center">
                  {getRankIcon(index + 1)}
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-8 w-8">
                      <AvatarImage
                        src={`${API_BASE_URL}/avatar/${item.id}`}
                        alt={item.name}
                        onLoad={() => console.log(`Avatar loaded for ${item.name} (ID: ${item.id})`)}
                        onError={() => console.log(`Avatar failed for ${item.name} (ID: ${item.id}), URL: ${API_BASE_URL}/avatar/${item.id}`)}
                      />
                      <AvatarFallback>
                        {item.name.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <span className="font-medium">{item.name}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant={getJobBadgeVariant(item.job || '')}>
                    {typeof item.job === 'number' ? getJobNameFromId(item.job) : item.job}
                  </Badge>
                </TableCell>
                <TableCell className="font-semibold">{item.level}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};
