import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { useScrollPosition } from '../../hooks/useScrollPosition';
import { Card, CardContent, CardHeader } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { Alert, AlertDescription } from '../ui/Alert';
import { 
  ArrowLeft, 
  Calendar, 
  User, 
  Eye, 
  Loader2, 
  AlertCircle,
  Clock,
  Edit
} from 'lucide-react';
import { newsService } from '../../api/services/news';
import { Notice } from '../../types';

// Markdown 渲染组件
const MarkdownContent: React.FC<{ content: string }> = ({ content }) => {
  const [htmlContent, setHtmlContent] = useState('');

  useEffect(() => {
    // 简单的 Markdown 解析（可以后续替换为更完整的解析器）
    const parseMarkdown = (text: string) => {
      return text
        .replace(/^### (.*$)/gim, '<h3 class="text-lg font-semibold mt-6 mb-3">$1</h3>')
        .replace(/^## (.*$)/gim, '<h2 class="text-xl font-semibold mt-8 mb-4">$1</h2>')
        .replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold mt-8 mb-4">$1</h1>')
        .replace(/\*\*(.*)\*\*/gim, '<strong class="font-semibold">$1</strong>')
        .replace(/\*(.*)\*/gim, '<em class="italic">$1</em>')
        .replace(/`([^`]+)`/gim, '<code class="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono">$1</code>')
        .replace(/\n\n/gim, '</p><p class="mb-4">')
        .replace(/\n/gim, '<br>')
        .replace(/^(.+)$/gim, '<p class="mb-4">$1</p>');
    };

    setHtmlContent(parseMarkdown(content));
  }, [content]);

  return (
    <div 
      className="prose prose-gray max-w-none"
      dangerouslySetInnerHTML={{ __html: htmlContent }}
    />
  );
};

const NoticeDetail: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const { saveScrollPosition } = useScrollPosition('/notice');
  const [notice, setNotice] = useState<Notice | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const loadNotice = async () => {
      if (!id) {
        setError(t('common:notice.detail.notFound'));
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const noticeData = await newsService.getNoticeDetail(parseInt(id));
        setNotice(noticeData);
        
        // 设置页面标题
        document.title = `MagicMS - ${noticeData.title}`;
        
        setError('');
      } catch (err) {
        console.error('Failed to load notice detail:', err);
        setError(t('common:notice.detail.notFound'));
      } finally {
        setLoading(false);
      }
    };

    loadNotice();
  }, [id, t]);

  const formatDate = (dateString: string) => {
    // 假设服务器返回的是东八区时间，需要转换为本地时区
    const serverDate = new Date(dateString + (dateString.includes('T') ? '' : 'T00:00:00+08:00'));
    const localTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    const formattedDate = serverDate.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    });

    return `${formattedDate} (${localTimeZone})`;
  };

  const getCategoryBadge = (category: string) => {
    const categoryMap: Record<string, { label: string; variant: 'default' | 'secondary' | 'destructive' | 'outline' }> = {
      update: { label: t('common:notice.categories.update'), variant: 'default' },
      event: { label: t('common:notice.categories.event'), variant: 'secondary' },
      maintenance: { label: t('common:notice.categories.maintenance'), variant: 'destructive' },
      important: { label: t('common:notice.categories.important'), variant: 'outline' },
    };

    const config = categoryMap[category] || { label: t('common:notice.categories.all'), variant: 'outline' as const };
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <span className="ml-2 text-lg">{t('common:notice.detail.loading')}</span>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !notice) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Link to="/notice">
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t('common:notice.detail.backToList')}
            </Button>
          </Link>
        </div>
        
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 返回按钮 */}
      <div className="mb-6">
        <Link
          to="/notice"
          onClick={() => {
            // 在导航前保存当前详情页的滚动位置
            saveScrollPosition();
          }}
        >
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('common:notice.detail.backToList')}
          </Button>
        </Link>
      </div>

      {/* 公告详情 */}
      <Card>
        <CardHeader className="border-b">
          <div className="space-y-4">
            {/* 标题和分类 */}
            <div className="flex items-start justify-between">
              <h1 className="text-3xl font-bold text-gray-900 flex-1 mr-4">
                {notice.title}
              </h1>
              {getCategoryBadge(notice.category || 'general')}
            </div>

            {/* 元信息 */}
            <div className="flex flex-wrap items-center gap-6 text-sm text-gray-500">
              <div className="flex items-center gap-1">
                <User className="h-4 w-4" />
                <span>{t('common:notice.detail.author')}: {notice.author}</span>
              </div>
              
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>{t('common:notice.detail.publishTime')}: {formatDate(notice.createdAt)}</span>
              </div>

              {notice.updatedAt && notice.updatedAt !== notice.createdAt && (
                <div className="flex items-center gap-1">
                  <Edit className="h-4 w-4" />
                  <span>{t('common:notice.detail.updateTime')}: {formatDate(notice.updatedAt)}</span>
                </div>
              )}

              {notice.viewCount !== undefined && (
                <div className="flex items-center gap-1">
                  <Eye className="h-4 w-4" />
                  <span>{t('common:notice.detail.views')}: {notice.viewCount}</span>
                </div>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-8">
          {/* 公告内容 */}
          <div className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
            <div className="prose prose-gray max-w-none">
              {notice.content ? (
                <MarkdownContent content={notice.content} />
              ) : (
                <div className="text-center py-12">
                  <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500 italic text-lg">暂无内容</p>
                </div>
              )}
            </div>
          </div>

          {/* 底部操作区域 */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4 text-sm text-gray-500">
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  <span>{t('common:notice.detail.lastUpdate')}: {formatDate(notice.updatedAt || notice.createdAt)}</span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Link
                  to="/notice"
                  onClick={() => {
                    // 在导航前保存当前详情页的滚动位置
                    saveScrollPosition();
                  }}
                >
                  <Button variant="outline" size="sm">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    {t('common:notice.detail.backToList')}
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default NoticeDetail;
