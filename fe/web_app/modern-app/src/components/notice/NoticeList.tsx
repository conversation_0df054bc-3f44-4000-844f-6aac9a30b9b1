import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { Alert, AlertDescription } from '../ui/Alert';
import { 
  Calendar, 
  User, 
  Eye, 
  ChevronRight, 
  Loader2, 
  AlertCircle,
  Pin,
  Clock
} from 'lucide-react';
import { newsService } from '../../api/services/news';
import { Notice } from '../../types';

const NoticeList: React.FC = () => {
  const { t } = useTranslation();


  const [notices, setNotices] = useState<Notice[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // 瀑布流加载的观察器引用
  const loadMoreRef = useRef<HTMLDivElement>(null);

  const loadNotices = async (pageNum: number = 1, append: boolean = false, category: string = 'all') => {
    try {
      if (!append) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const params: any = {
        page: pageNum,
        size: 10
      };

      // 只有当分类不是 'all' 时才添加分类参数
      if (category !== 'all') {
        params.category = category;
      }

      const response = await newsService.getNoticeList(params);

      if (append) {
        setNotices(prev => [...prev, ...response.data]);
      } else {
        setNotices(response.data);
      }

      setHasMore(pageNum < Number(response.total / 10));
      setError('');
    } catch (err) {
      console.error('Failed to load notices:', err);
      setError(t('common:notice.list.loading'));
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  useEffect(() => {
    loadNotices(1, false, selectedCategory);
  }, [selectedCategory]);



  const handleLoadMore = useCallback(() => {
    if (loadingMore || !hasMore) return;

    const nextPage = page + 1;
    setPage(nextPage);
    loadNotices(nextPage, true, selectedCategory);
  }, [page, loadingMore, hasMore, selectedCategory]);

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    setPage(1);
    setNotices([]);
    setHasMore(true);
  };

  // 设置Intersection Observer来实现瀑布流自动加载
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        if (target.isIntersecting && hasMore && !loadingMore && !loading) {
          handleLoadMore();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '100px' // 提前100px开始加载
      }
    );

    if (loadMoreRef.current && hasMore) {
      observer.observe(loadMoreRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [handleLoadMore, hasMore, loadingMore, loading, page]);

  const formatDate = (dateString: string) => {
    // 假设服务器返回的是东八区时间，需要转换为本地时区
    const serverDate = new Date(dateString + (dateString.includes('T') ? '' : 'T00:00:00+08:00'));
    const localTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    const formattedDate = serverDate.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    });

    return `${formattedDate} (${localTimeZone})`;
  };

  const getCategoryBadge = (category: string) => {
    const categoryMap: Record<string, { label: string; variant: 'default' | 'secondary' | 'destructive' | 'outline' }> = {
      update: { label: t('common:notice.categories.update'), variant: 'default' },
      event: { label: t('common:notice.categories.event'), variant: 'secondary' },
      maintenance: { label: t('common:notice.categories.maintenance'), variant: 'destructive' },
      important: { label: t('common:notice.categories.important'), variant: 'outline' },
    };

    const config = categoryMap[category] || { label: t('common:notice.categories.all'), variant: 'outline' as const };
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getCategoryFilterButtons = () => {
    const categories = [
      { key: 'all', label: t('common:notice.categories.all'), variant: 'outline' as const },
      { key: 'update', label: t('common:notice.categories.update'), variant: 'default' as const },
      { key: 'event', label: t('common:notice.categories.event'), variant: 'secondary' as const },
      { key: 'maintenance', label: t('common:notice.categories.maintenance'), variant: 'destructive' as const },
      { key: 'important', label: t('common:notice.categories.important'), variant: 'outline' as const },
    ];

    return (
      <div className="flex flex-wrap gap-2 mb-6">
        {categories.map((category) => (
          <Button
            key={category.key}
            variant={selectedCategory === category.key ? category.variant : 'outline'}
            size="sm"
            onClick={() => handleCategoryChange(category.key)}
            className={selectedCategory === category.key ? 'ring-2 ring-offset-2' : ''}
          >
            {category.label}
          </Button>
        ))}
      </div>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <span className="ml-2 text-lg">{t('common:notice.list.loading')}</span>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (notices.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-lg text-gray-600">{t('common:notice.list.empty')}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <CardHeader className="px-0">
        <CardTitle className="text-2xl">{t('common:notice.list.title')}</CardTitle>
      </CardHeader>

      {/* 分类筛选器 */}
      {getCategoryFilterButtons()}

      {notices.map((notice) => (
        <Card key={notice.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-3">
                  {notice.isTop && (
                    <Pin className="h-4 w-4 text-red-500" />
                  )}
                  <h3 className="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors">
                    <Link
                      to={`/notice/${notice.id}`}
                    >
                      {notice.title}
                    </Link>
                  </h3>
                  {getCategoryBadge(notice.category || 'general')}
                </div>

                <div className="flex items-center gap-6 text-sm text-gray-500 mb-3">
                  <div className="flex items-center gap-1">
                    <User className="h-4 w-4" />
                    <span>{notice.author}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    <span>{formatDate(notice.createdAt)}</span>
                  </div>
                  {notice.viewCount !== undefined && (
                    <div className="flex items-center gap-1">
                      <Eye className="h-4 w-4" />
                      <span>{notice.viewCount}</span>
                    </div>
                  )}
                </div>

                {notice.content && (
                  <p className="text-gray-600 line-clamp-2 mb-3">
                    {notice.content.replace(/[#*`]/g, '').substring(0, 150)}...
                  </p>
                )}
              </div>

              <Link
                to={`/notice/${notice.id}`}
              >
                <Button variant="ghost" size="sm" className="ml-4">
                  {t('common:notice.list.viewDetail')}
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      ))}

      {/* 瀑布流加载触发器 */}
      {hasMore && (
        <div
          ref={loadMoreRef}
          className="text-center pt-6 pb-4"
        >
          {loadingMore && (
            <div className="flex items-center justify-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-gray-500">{t('common:notice.list.loading')}</span>
            </div>
          )}
        </div>
      )}

      {/* 没有更多数据提示 */}
      {!hasMore && notices.length > 0 && (
        <div className="text-center pt-6 pb-4">
          <p className="text-gray-500 text-sm">已加载全部公告 (共{notices.length}条)</p>
        </div>
      )}


    </div>
  );
};

export default NoticeList;
