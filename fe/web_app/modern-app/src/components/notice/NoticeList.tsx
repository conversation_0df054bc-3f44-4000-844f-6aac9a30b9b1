import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { useScrollRestore } from '../../hooks/useScrollPosition';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { Alert, AlertDescription } from '../ui/Alert';
import { 
  Calendar, 
  User, 
  Eye, 
  ChevronRight, 
  Loader2, 
  AlertCircle,
  Pin,
  Clock
} from 'lucide-react';
import { newsService } from '../../api/services/news';
import { Notice } from '../../types';

const NoticeList: React.FC = () => {
  const { t } = useTranslation();

  // 恢复滚动位置
  useScrollRestore('/notice');
  const [notices, setNotices] = useState<Notice[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const loadNotices = async (pageNum: number = 1, append: boolean = false, category: string = 'all') => {
    try {
      if (!append) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const params: any = {
        page: pageNum,
        size: 10
      };

      // 只有当分类不是 'all' 时才添加分类参数
      if (category !== 'all') {
        params.category = category;
      }

      const response = await newsService.getNoticeList(params);

      if (append) {
        setNotices(prev => [...prev, ...response.data]);
      } else {
        setNotices(response.data);
      }

      setHasMore(pageNum < response.totalPages);
      setError('');
    } catch (err) {
      console.error('Failed to load notices:', err);
      setError(t('common:notice.list.loading'));
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  useEffect(() => {
    loadNotices(1, false, selectedCategory);
  }, [selectedCategory]);

  const handleLoadMore = () => {
    const nextPage = page + 1;
    setPage(nextPage);
    loadNotices(nextPage, true, selectedCategory);
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    setPage(1);
    setNotices([]);
    setHasMore(true);
  };

  const formatDate = (dateString: string) => {
    // 假设服务器返回的是东八区时间，需要转换为本地时区
    const serverDate = new Date(dateString + (dateString.includes('T') ? '' : 'T00:00:00+08:00'));
    const localTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    const formattedDate = serverDate.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    });

    return `${formattedDate} (${localTimeZone})`;
  };

  const getCategoryBadge = (category: string) => {
    const categoryMap: Record<string, { label: string; variant: 'default' | 'secondary' | 'destructive' | 'outline' }> = {
      update: { label: t('common:notice.categories.update'), variant: 'default' },
      event: { label: t('common:notice.categories.event'), variant: 'secondary' },
      maintenance: { label: t('common:notice.categories.maintenance'), variant: 'destructive' },
      important: { label: t('common:notice.categories.important'), variant: 'outline' },
    };

    const config = categoryMap[category] || { label: t('common:notice.categories.all'), variant: 'outline' as const };
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getCategoryFilterButtons = () => {
    const categories = [
      { key: 'all', label: t('common:notice.categories.all'), variant: 'outline' as const },
      { key: 'update', label: t('common:notice.categories.update'), variant: 'default' as const },
      { key: 'event', label: t('common:notice.categories.event'), variant: 'secondary' as const },
      { key: 'maintenance', label: t('common:notice.categories.maintenance'), variant: 'destructive' as const },
      { key: 'important', label: t('common:notice.categories.important'), variant: 'outline' as const },
    ];

    return (
      <div className="flex flex-wrap gap-2 mb-6">
        {categories.map((category) => (
          <Button
            key={category.key}
            variant={selectedCategory === category.key ? category.variant : 'outline'}
            size="sm"
            onClick={() => handleCategoryChange(category.key)}
            className={selectedCategory === category.key ? 'ring-2 ring-offset-2' : ''}
          >
            {category.label}
          </Button>
        ))}
      </div>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <span className="ml-2 text-lg">{t('common:notice.list.loading')}</span>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (notices.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-lg text-gray-600">{t('common:notice.list.empty')}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <CardHeader className="px-0">
        <CardTitle className="text-2xl">{t('common:notice.list.title')}</CardTitle>
      </CardHeader>

      {/* 分类筛选器 */}
      {getCategoryFilterButtons()}

      {notices.map((notice) => (
        <Card key={notice.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-3">
                  {notice.isTop && (
                    <Pin className="h-4 w-4 text-red-500" />
                  )}
                  <h3 className="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors">
                    <Link to={`/notice/${notice.id}`}>
                      {notice.title}
                    </Link>
                  </h3>
                  {getCategoryBadge(notice.category || 'general')}
                </div>

                <div className="flex items-center gap-6 text-sm text-gray-500 mb-3">
                  <div className="flex items-center gap-1">
                    <User className="h-4 w-4" />
                    <span>{notice.author}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    <span>{formatDate(notice.createdAt)}</span>
                  </div>
                  {notice.viewCount !== undefined && (
                    <div className="flex items-center gap-1">
                      <Eye className="h-4 w-4" />
                      <span>{notice.viewCount}</span>
                    </div>
                  )}
                </div>

                {notice.content && (
                  <p className="text-gray-600 line-clamp-2 mb-3">
                    {notice.content.replace(/[#*`]/g, '').substring(0, 150)}...
                  </p>
                )}
              </div>

              <Link to={`/notice/${notice.id}`}>
                <Button variant="ghost" size="sm" className="ml-4">
                  {t('common:notice.list.viewDetail')}
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      ))}

      {hasMore && (
        <div className="text-center pt-6">
          <Button
            onClick={handleLoadMore}
            disabled={loadingMore}
            variant="outline"
            size="lg"
          >
            {loadingMore ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                {t('common:notice.list.loading')}
              </>
            ) : (
              <>
                <Clock className="h-4 w-4 mr-2" />
                {t('common:notice.list.loadMore')}
              </>
            )}
          </Button>
        </div>
      )}
    </div>
  );
};

export default NoticeList;
