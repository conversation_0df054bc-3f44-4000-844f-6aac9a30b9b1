import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { ROUTES } from '../../lib/router';

const Footer = () => {
  const { t } = useTranslation();
  const currentYear = new Date().getFullYear();

  return (
    <footer className="border-t bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and Description */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
                <span className="text-primary-foreground font-bold text-sm">M</span>
              </div>
              <span className="font-bold text-xl">MagicMS</span>
            </div>
            <p className="text-sm text-muted-foreground">
              {t('common:home.description')}
            </p>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="font-semibold">快速链接</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link 
                  to={ROUTES.HOME} 
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  {t('nav.home')}
                </Link>
              </li>
              <li>
                <Link 
                  to={ROUTES.DOWNLOAD} 
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  {t('nav.download')}
                </Link>
              </li>
              <li>
                <Link 
                  to={ROUTES.RANKING} 
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  {t('nav.ranking')}
                </Link>
              </li>
              <li>
                <Link 
                  to={ROUTES.NOTICE} 
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  {t('nav.notice')}
                </Link>
              </li>
            </ul>
          </div>

          {/* Game Features */}
          <div className="space-y-4">
            <h3 className="font-semibold">游戏特色</h3>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>经验倍率 5x</li>
              <li>金币倍率 3x</li>
              <li>掉落倍率 2x</li>
              <li>最高等级 255</li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="font-semibold">联系我们</h3>
            <div className="space-y-2 text-sm text-muted-foreground">
              <p>QQ群: 123456789</p>
              <p>邮箱: <EMAIL></p>
              <p>在线时间: 24/7</p>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-muted-foreground">
            © {currentYear} MagicMS. All rights reserved.
          </p>
          <div className="flex space-x-4 mt-4 md:mt-0">
            <Link 
              to="/privacy" 
              className="text-sm text-muted-foreground hover:text-primary transition-colors"
            >
              隐私政策
            </Link>
            <Link 
              to="/terms" 
              className="text-sm text-muted-foreground hover:text-primary transition-colors"
            >
              服务条款
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
