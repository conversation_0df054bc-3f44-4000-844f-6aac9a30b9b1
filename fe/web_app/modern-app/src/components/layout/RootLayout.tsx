import { Outlet } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import Header from './Header';
import Footer from './Footer';
import { Toaster } from 'react-hot-toast';

const RootLayout = () => {
  const { i18n } = useTranslation();

  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* 设置HTML语言属性 */}
      <div className="flex flex-col min-h-screen" lang={i18n.language}>
        {/* 头部导航 */}
        <Header />
        
        {/* 主要内容区域 */}
        <main className="flex-1">
          <Outlet />
        </main>
        
        {/* 页脚 */}
        <Footer />
        
        {/* 全局通知组件 */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: 'hsl(var(--background))',
              color: 'hsl(var(--foreground))',
              border: '1px solid hsl(var(--border))',
            },
          }}
        />
      </div>
    </div>
  );
};

export default RootLayout;
