import React from 'react';
import { useTranslation } from 'react-i18next';
import { Trophy, Gift, Star } from 'lucide-react';

const VoteHero: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="relative bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800 text-white py-16 px-4 rounded-lg mb-8">
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-black/20 rounded-lg"></div>
      <div className="absolute top-4 right-4 opacity-20">
        <Trophy className="h-24 w-24" />
      </div>
      <div className="absolute bottom-4 left-4 opacity-20">
        <Gift className="h-20 w-20" />
      </div>
      
      <div className="relative z-10 max-w-4xl mx-auto text-center">
        <div className="flex justify-center mb-6">
          <div className="bg-white/20 p-4 rounded-full">
            <Star className="h-12 w-12 text-yellow-300" />
          </div>
        </div>
        
        <h1 className="text-4xl md:text-5xl font-bold mb-4">
          {t('common:vote.title')}
        </h1>
        
        <p className="text-xl md:text-2xl mb-6 text-blue-100">
          {t('common:vote.subtitle')}
        </p>
        
        <p className="text-lg text-blue-200 max-w-2xl mx-auto">
          {t('common:vote.description')}
        </p>
      </div>
    </div>
  );
};

export default VoteHero;
