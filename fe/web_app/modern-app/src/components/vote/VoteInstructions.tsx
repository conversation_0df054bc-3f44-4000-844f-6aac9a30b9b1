import React from 'react';
import { useTranslation } from 'react-i18next';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '../ui/Card';
import { Alert, AlertDescription } from '../ui/Alert';
import { CheckCircle, AlertTriangle, Clock, Shield } from 'lucide-react';

const VoteInstructions: React.FC = () => {
  const { t } = useTranslation();

  const steps = [
    {
      icon: <CheckCircle className="h-5 w-5 text-green-600" />,
      text: t('common:vote.instructions.step1')
    },
    {
      icon: <CheckCircle className="h-5 w-5 text-green-600" />,
      text: t('common:vote.instructions.step2')
    },
    {
      icon: <CheckCircle className="h-5 w-5 text-green-600" />,
      text: t('common:vote.instructions.step3')
    },
    {
      icon: <CheckCircle className="h-5 w-5 text-green-600" />,
      text: t('common:vote.instructions.step4')
    },
    {
      icon: <CheckCircle className="h-5 w-5 text-green-600" />,
      text: t('common:vote.instructions.step5')
    }
  ];

  const warnings = [
    {
      icon: <Shield className="h-5 w-5 text-orange-500" />,
      text: t('common:vote.warnings.complete')
    },
    {
      icon: <Clock className="h-5 w-5 text-blue-500" />,
      text: t('common:vote.warnings.delay')
    },
    {
      icon: <AlertTriangle className="h-5 w-5 text-red-500" />,
      text: t('common:vote.warnings.account')
    }
  ];

  return (
    <div className="grid md:grid-cols-2 gap-6 mb-8">
      {/* 投票步骤 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-6 w-6 text-green-600" />
            {t('common:vote.instructions.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ol className="space-y-3">
            {steps.map((step, index) => (
              <li key={index} className="flex items-start gap-3">
                <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                  {index + 1}
                </span>
                <div className="flex items-start gap-2 flex-1">
                  {step.icon}
                  <span className="text-sm text-gray-700">{step.text}</span>
                </div>
              </li>
            ))}
          </ol>
        </CardContent>
      </Card>

      {/* 重要提醒 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-6 w-6 text-orange-500" />
            {t('common:vote.warnings.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {warnings.map((warning, index) => (
              <Alert key={index} className="border-orange-200 bg-orange-50">
                <div className="flex items-start gap-2">
                  {warning.icon}
                  <AlertDescription className="text-sm text-gray-700">
                    {warning.text}
                  </AlertDescription>
                </div>
              </Alert>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default VoteInstructions;
