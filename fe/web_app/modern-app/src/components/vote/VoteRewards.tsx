import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { Coins, TrendingUp, Award } from 'lucide-react';

const VoteRewards: React.FC = () => {
  const { t } = useTranslation();

  const rewards = [
    {
      icon: <Coins className="h-8 w-8 text-yellow-500" />,
      title: t('common:vote.rewards.daily'),
      description: "每日基础奖励",
      badge: "10,000 NX",
      color: "bg-yellow-50 border-yellow-200"
    },
    {
      icon: <TrendingUp className="h-8 w-8 text-green-500" />,
      title: t('common:vote.rewards.consecutive'),
      description: "连续投票额外奖励",
      badge: "+500 NX/天",
      color: "bg-green-50 border-green-200"
    },
    {
      icon: <Award className="h-8 w-8 text-purple-500" />,
      title: t('common:vote.rewards.maximum'),
      description: "最高累计奖励上限",
      badge: "20,000 NX",
      color: "bg-purple-50 border-purple-200"
    }
  ];

  return (
    <div className="mb-8">
      <h2 className="text-2xl font-bold text-center mb-6">
        {t('common:vote.rewards.title')}
      </h2>
      
      <div className="grid md:grid-cols-3 gap-6">
        {rewards.map((reward, index) => (
          <Card key={index} className={`${reward.color} hover:shadow-lg transition-shadow`}>
            <CardHeader className="text-center pb-3">
              <div className="flex justify-center mb-3">
                {reward.icon}
              </div>
              <CardTitle className="text-lg">
                <Badge variant="secondary" className="text-lg px-3 py-1">
                  {reward.badge}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="font-medium text-gray-800 mb-2">
                {reward.title}
              </p>
              <p className="text-sm text-gray-600">
                {reward.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default VoteRewards;
