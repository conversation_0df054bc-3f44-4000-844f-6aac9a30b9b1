import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Alert, AlertDescription } from '../ui/Alert';
import { ExternalLink, User, AlertTriangle } from 'lucide-react';
import { gameService } from '../../api/services/game';

const VoteForm: React.FC = () => {
  const { t } = useTranslation();
  const [username, setUsername] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!username.trim()) {
      setError(t('common:vote.errors.noAccount'));
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      const result = await gameService.submitVote(username.trim());
      // 跳转到投票页面
      window.location.href = result.url;
    } catch (err) {
      console.error('Vote submission failed:', err);
      setError(t('common:vote.errors.serverError'));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="flex justify-center mb-3">
          <div className="bg-blue-100 p-3 rounded-full">
            <User className="h-8 w-8 text-blue-600" />
          </div>
        </div>
        <CardTitle className="text-xl">
          {t('common:vote.form.accountLabel')}
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Input
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              placeholder={t('common:vote.form.accountPlaceholder')}
              maxLength={15}
              className="text-center text-lg"
              disabled={isSubmitting}
            />
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Button
            type="submit"
            className="w-full text-lg py-3"
            disabled={isSubmitting || !username.trim()}
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                {t('common:vote.form.submitting')}
              </>
            ) : (
              <>
                <ExternalLink className="h-5 w-5 mr-2" />
                {t('common:vote.form.submitButton')}
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default VoteForm;
