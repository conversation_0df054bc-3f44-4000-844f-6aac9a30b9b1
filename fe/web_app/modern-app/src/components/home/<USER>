import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui';
import { ServerStatus } from '../game';
import { TrendingUp, Coins, Gift, Crown } from 'lucide-react';

const ServerInfoSection = () => {
  const { t } = useTranslation();

  const serverRates = [
    { 
      name: t('common:home.serverInfo.expRate'), 
      value: "5x", 
      color: "text-green-600",
      bgColor: "bg-green-50 dark:bg-green-950",
      icon: <TrendingUp className="h-6 w-6" />
    },
    { 
      name: t('common:home.serverInfo.mesoRate'), 
      value: "3x", 
      color: "text-yellow-600",
      bgColor: "bg-yellow-50 dark:bg-yellow-950",
      icon: <Coins className="h-6 w-6" />
    },
    { 
      name: t('common:home.serverInfo.dropRate'), 
      value: "2x", 
      color: "text-blue-600",
      bgColor: "bg-blue-50 dark:bg-blue-950",
      icon: <Gift className="h-6 w-6" />
    },
    { 
      name: t('common:home.serverInfo.bossRate'), 
      value: "2x", 
      color: "text-red-600",
      bgColor: "bg-red-50 dark:bg-red-950",
      icon: <Crown className="h-6 w-6" />
    },
  ];

  return (
    <section className="py-16 lg:py-24">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">
            {t('common:home.serverInfo.title')}
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            {t('common:home.serverInfo.description')}
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {serverRates.map((rate, index) => (
            <Card key={index} className="text-center group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
              <CardHeader className="pb-3">
                <div className={`mx-auto mb-3 p-3 rounded-full ${rate.bgColor} ${rate.color} group-hover:scale-110 transition-transform duration-300`}>
                  {rate.icon}
                </div>
                <CardTitle className={`text-3xl font-bold ${rate.color}`}>
                  {rate.value}
                </CardTitle>
                <CardDescription className="font-medium">
                  {rate.name}
                </CardDescription>
              </CardHeader>
            </Card>
          ))}
        </div>

        {/* Server Status Component */}
        <div className="max-w-4xl mx-auto">
          <ServerStatus className="shadow-lg" />
        </div>
      </div>
    </section>
  );
};

export default ServerInfoSection;
