import { useTranslation } from 'react-i18next';
import { RankingTable } from '../game';
import { Button } from '../ui/Button';
import { Link } from 'react-router-dom';
import { ROUTES } from '../../lib/router';
import { Trophy, ArrowRight } from 'lucide-react';

const RankingsSection = () => {
  const { t } = useTranslation();

  return (
    <section className="py-16 lg:py-24 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <div className="p-3 rounded-full bg-primary/10 text-primary mr-3">
              <Trophy className="h-8 w-8" />
            </div>
            <h2 className="text-3xl lg:text-4xl font-bold">
              {t('common:home.rankings.title')}
            </h2>
          </div>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            {t('common:home.rankings.description')}
          </p>
        </div>
        
        <div className="max-w-6xl mx-auto">
          <RankingTable />
          
          {/* View All Rankings Button */}
          <div className="text-center mt-8">
            <Button size="lg" variant="outline" asChild>
              <Link to={ROUTES.RANKING}>
                {t('common:home.rankings.viewAll')}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default RankingsSection;
