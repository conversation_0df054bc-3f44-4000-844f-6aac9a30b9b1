import React from 'react';
import { useTranslation } from 'react-i18next';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/Select';
import { Card, CardContent } from '../ui/Card';
import { RankingFilter } from '../../types';

interface RankingFiltersProps {
  filter: RankingFilter;
  onFilterChange: (filter: RankingFilter) => void;
  disabled?: boolean;
}

export const RankingFilters: React.FC<RankingFiltersProps> = ({
  filter,
  onFilterChange,
  disabled = false
}) => {
  const { t } = useTranslation('common');

  const jobOptions = [
    { value: 'all', label: t('ranking.filters.all') },
    { value: 'beginner', label: t('ranking.filters.beginner') },
    { value: 'warrior', label: t('ranking.filters.warrior') },
    { value: 'magician', label: t('ranking.filters.magician') },
    { value: 'bowman', label: t('ranking.filters.bowman') },
    { value: 'thief', label: t('ranking.filters.thief') },
    { value: 'pirate', label: t('ranking.filters.pirate') },
    { value: 'cygnus', label: t('ranking.filters.cygnus') },
    { value: 'aran', label: t('ranking.filters.aran') },
  ];

  const sortOptions = [
    { value: 'level', label: t('ranking.tabs.level') },
    { value: 'fame', label: t('ranking.tabs.fame') },
    { value: 'quest', label: t('ranking.tabs.quest') },
    { value: 'monsterbook', label: t('ranking.tabs.monsterbook') },
    { value: 'guild', label: t('ranking.tabs.guild') },
  ];

  const handleJobChange = (job: string) => {
    onFilterChange({
      ...filter,
      job: job as RankingFilter['job'],
      page: 1, // 重置到第一页
    });
  };

  const handleSortChange = (sort: string) => {
    onFilterChange({
      ...filter,
      sort: sort as RankingFilter['sort'],
      page: 1, // 重置到第一页
    });
  };

  return (
    <Card className="mb-6">
      <CardContent className="pt-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('ranking.filters.job')}
            </label>
            <Select
              value={filter.job || 'all'}
              onValueChange={handleJobChange}
              disabled={disabled}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('ranking.filters.job')} />
              </SelectTrigger>
              <SelectContent>
                {jobOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('ranking.filters.sort')}
            </label>
            <Select
              value={filter.sort || 'level'}
              onValueChange={handleSortChange}
              disabled={disabled}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('ranking.filters.sort')} />
              </SelectTrigger>
              <SelectContent>
                {sortOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
