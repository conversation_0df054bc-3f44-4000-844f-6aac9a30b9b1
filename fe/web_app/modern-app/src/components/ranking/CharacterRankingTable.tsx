import React from 'react';
import { useTranslation } from 'react-i18next';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/Table';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/Avatar';
import { Badge } from '../ui/Badge';
import { RankIcon } from './RankIcon';
import { JobIcon } from './JobIcon';
import { RankingItem } from '../../types';

interface CharacterRankingTableProps {
  data: RankingItem[];
  sortType: 'level' | 'fame' | 'quest' | 'monsterbook';
  loading?: boolean;
}

export const CharacterRankingTable: React.FC<CharacterRankingTableProps> = ({
  data,
  sortType,
  loading = false
}) => {
  const { t } = useTranslation('common');

  const formatValue = (value: number, type: string) => {
    if (type === 'level') {
      return `Lv.${value}`;
    }
    if (type === 'fame' || type === 'quest_count' || type === 'monster_book') {
      return value.toLocaleString();
    }
    if (type === 'exp') {
      return value.toLocaleString();
    }
    return value.toString();
  };

  const getValueColumn = () => {
    switch (sortType) {
      case 'level':
        return { key: 'level', label: t('ranking.table.level') };
      case 'fame':
        return { key: 'fame', label: t('ranking.table.fame') };
      case 'quest':
        return { key: 'quest_count', label: t('ranking.table.quest') };
      case 'monsterbook':
        return { key: 'monster_book', label: t('ranking.table.monsterbook') };
      default:
        return { key: 'level', label: t('ranking.table.level') };
    }
  };

  const valueColumn = getValueColumn();

  if (loading) {
    return (
      <div className="space-y-3">
        {[...Array(10)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-20 text-center">{t('ranking.table.rank')}</TableHead>
            <TableHead className="w-16 text-center">{t('ranking.table.avatar')}</TableHead>
            <TableHead>{t('ranking.table.name')}</TableHead>
            <TableHead className="w-20 text-center">{t('ranking.table.job')}</TableHead>
            <TableHead className="w-24 text-center">{t('ranking.table.level')}</TableHead>
            <TableHead className="w-32 text-center">{valueColumn.label}</TableHead>
            <TableHead className="hidden md:table-cell">{t('ranking.table.guild')}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((item) => (
            <TableRow key={item.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
              <TableCell className="text-center">
                <RankIcon rank={item.rank} />
              </TableCell>
              <TableCell className="text-center">
                <Avatar className="h-10 w-10 mx-auto">
                  <AvatarImage 
                    src={item.avatar || `/api/avatar/${item.id}`} 
                    alt={item.name} 
                  />
                  <AvatarFallback>
                    {item.name.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
              </TableCell>
              <TableCell>
                <div className="font-medium text-orange-600 dark:text-orange-400">
                  {item.name}
                </div>
              </TableCell>
              <TableCell className="text-center">
                <JobIcon jobId={item.job} />
              </TableCell>
              <TableCell className="text-center">
                <Badge variant="secondary" className="font-mono">
                  {item.level}
                </Badge>
              </TableCell>
              <TableCell className="text-center font-semibold">
                {formatValue(item.value, valueColumn.key)}
              </TableCell>
              <TableCell className="hidden md:table-cell">
                {item.guild ? (
                  <div className="flex items-center space-x-2">
                    <img
                      src={`https://maplestory.io/api/GMS/83/GuildMark/background/${item.guild.logoBG}/${item.guild.logoBGColor}/mark/${item.guild.logo}/${item.guild.logoColor}`}
                      alt={item.guild.name}
                      className="w-6 h-6"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                    <span className="text-sm text-blue-600 dark:text-blue-400">
                      {item.guild.name}
                    </span>
                    {item.guild.alliance_name && (
                      <span className="text-xs text-gray-500">
                        [{item.guild.alliance_name}]
                      </span>
                    )}
                  </div>
                ) : (
                  <span className="text-gray-400 text-sm">-</span>
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
