import React from 'react';
import { useTranslation } from 'react-i18next';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/Table';
import { Badge } from '../ui/Badge';
import { RankIcon } from './RankIcon';
import { GuildRankingItem } from '../../types';

interface GuildRankingTableProps {
  data: GuildRankingItem[];
  loading?: boolean;
}

export const GuildRankingTable: React.FC<GuildRankingTableProps> = ({
  data,
  loading = false
}) => {
  const { t } = useTranslation('common');

  if (loading) {
    return (
      <div className="space-y-3">
        {[...Array(10)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-20 text-center">{t('ranking.table.rank')}</TableHead>
            <TableHead>{t('ranking.table.guildName')}</TableHead>
            <TableHead className="hidden md:table-cell">{t('ranking.table.leader')}</TableHead>
            <TableHead className="w-24 text-center">{t('ranking.table.members')}</TableHead>
            <TableHead className="w-32 text-center">{t('ranking.table.points')}</TableHead>
            <TableHead className="hidden lg:table-cell max-w-xs">{t('ranking.table.notice')}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((item) => (
            <TableRow key={item.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
              <TableCell className="text-center">
                <RankIcon rank={item.rank} />
              </TableCell>
              <TableCell>
                <div className="flex items-center space-x-3">
                  <img
                    src={`https://maplestory.io/api/GMS/83/GuildMark/background/${item.logo_bg}/${item.logo_bg_color}/mark/${item.logo}/${item.logo_color}`}
                    alt={item.name}
                    className="w-8 h-8"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                  <div>
                    <div className="font-medium text-blue-600 dark:text-blue-400">
                      {item.name}
                    </div>
                    {item.alliance && (
                      <div className="text-xs text-gray-500">
                        联盟: {item.alliance}
                      </div>
                    )}
                  </div>
                </div>
              </TableCell>
              <TableCell className="hidden md:table-cell">
                <span className="text-orange-600 dark:text-orange-400 font-medium">
                  {item.leader}
                </span>
              </TableCell>
              <TableCell className="text-center">
                <Badge variant="outline" className="font-mono">
                  {item.memberCount}
                </Badge>
              </TableCell>
              <TableCell className="text-center font-semibold">
                {item.value.toLocaleString()}
              </TableCell>
              <TableCell className="hidden lg:table-cell max-w-xs">
                {item.notice ? (
                  <div className="text-sm text-gray-600 dark:text-gray-400 truncate" title={item.notice}>
                    {item.notice.startsWith('[p]') ? item.notice.substring(3) : '私密公告'}
                  </div>
                ) : (
                  <span className="text-gray-400 text-sm">-</span>
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
