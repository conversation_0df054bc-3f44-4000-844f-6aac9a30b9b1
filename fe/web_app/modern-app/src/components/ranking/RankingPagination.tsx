import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '../ui/Button';
import { ChevronLeft, ChevronRight, RefreshCw } from 'lucide-react';

interface RankingPaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
  onRefresh?: () => void;
  loading?: boolean;
}

export const RankingPagination: React.FC<RankingPaginationProps> = ({
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  onPageChange,
  onRefresh,
  loading = false
}) => {
  const { t } = useTranslation('common');

  const handlePrevPage = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    // 调整起始页，确保显示足够的页码
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    return pages;
  };

  const pageNumbers = getPageNumbers();

  return (
    <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6">
      {/* 统计信息 */}
      <div className="text-sm text-gray-600 dark:text-gray-400">
        {t('ranking.pagination.total')} {totalItems} {t('ranking.pagination.items')}
        {totalPages > 0 && (
          <>
            {' • '}
            {t('ranking.pagination.page')} {currentPage} / {totalPages}
          </>
        )}
      </div>

      {/* 分页控件 */}
      <div className="flex items-center gap-2">
        {/* 刷新按钮 */}
        {onRefresh && (
          <Button
            variant="outline"
            size="sm"
            onClick={onRefresh}
            disabled={loading}
            className="mr-2"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span className="hidden sm:inline ml-1">{t('ranking.refresh')}</span>
          </Button>
        )}

        {/* 上一页 */}
        <Button
          variant="outline"
          size="sm"
          onClick={handlePrevPage}
          disabled={currentPage <= 1 || loading}
        >
          <ChevronLeft className="w-4 h-4" />
          <span className="hidden sm:inline ml-1">{t('ranking.pagination.prev')}</span>
        </Button>

        {/* 页码 */}
        <div className="flex items-center gap-1">
          {pageNumbers.map((pageNum) => (
            <Button
              key={pageNum}
              variant={pageNum === currentPage ? "default" : "outline"}
              size="sm"
              onClick={() => onPageChange(pageNum)}
              disabled={loading}
              className="w-10"
            >
              {pageNum}
            </Button>
          ))}
        </div>

        {/* 下一页 */}
        <Button
          variant="outline"
          size="sm"
          onClick={handleNextPage}
          disabled={currentPage >= totalPages || loading}
        >
          <span className="hidden sm:inline mr-1">{t('ranking.pagination.next')}</span>
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
};
