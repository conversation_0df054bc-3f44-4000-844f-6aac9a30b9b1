import React from 'react';
import { useTranslation } from 'react-i18next';

interface JobIconProps {
  jobId: number;
  className?: string;
  showTooltip?: boolean;
}

export const JobIcon: React.FC<JobIconProps> = ({ 
  jobId, 
  className = "w-6 h-6", 
  showTooltip = true 
}) => {
  const { t } = useTranslation('common');

  const getJobInfo = (jobId: number) => {
    if (jobId === 0) {
      return { icon: '/static/images/beginner.png', name: t('ranking.filters.beginner') };
    } else if (jobId >= 100 && jobId < 200) {
      return { icon: '/static/images/warrior.png', name: t('ranking.filters.warrior') };
    } else if (jobId >= 200 && jobId < 300) {
      return { icon: '/static/images/magician.png', name: t('ranking.filters.magician') };
    } else if (jobId >= 300 && jobId < 400) {
      return { icon: '/static/images/bowman.png', name: t('ranking.filters.bowman') };
    } else if (jobId >= 400 && jobId < 500) {
      return { icon: '/static/images/thief.png', name: t('ranking.filters.thief') };
    } else if (jobId >= 500 && jobId < 600) {
      return { icon: '/static/images/pirate.png', name: t('ranking.filters.pirate') };
    } else if (jobId >= 1000 && jobId < 1600) {
      return { icon: '/static/images/cygnus.png', name: t('ranking.filters.cygnus') };
    } else if (jobId >= 2000) {
      return { icon: '/static/images/aran.png', name: t('ranking.filters.aran') };
    }
    return { icon: '/static/images/beginner.png', name: 'Unknown' };
  };

  const jobInfo = getJobInfo(jobId);

  return (
    <img
      src={jobInfo.icon}
      alt={jobInfo.name}
      title={showTooltip ? jobInfo.name : undefined}
      className={className}
      onError={(e) => {
        e.currentTarget.style.display = 'none';
      }}
    />
  );
};
