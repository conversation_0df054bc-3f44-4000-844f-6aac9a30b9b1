import React from 'react';
import { Crown, Medal, Trophy } from 'lucide-react';

interface RankIconProps {
  rank: number;
  className?: string;
}

export const RankIcon: React.FC<RankIconProps> = ({ rank, className = "w-5 h-5" }) => {
  if (rank === 1) {
    return (
      <div className="flex items-center justify-center">
        <Crown className={`${className} text-yellow-500`} />
        <span className="ml-1 font-bold text-yellow-600">1</span>
      </div>
    );
  }
  
  if (rank === 2) {
    return (
      <div className="flex items-center justify-center">
        <Trophy className={`${className} text-gray-400`} />
        <span className="ml-1 font-bold text-gray-600">2</span>
      </div>
    );
  }
  
  if (rank === 3) {
    return (
      <div className="flex items-center justify-center">
        <Medal className={`${className} text-amber-600`} />
        <span className="ml-1 font-bold text-amber-700">3</span>
      </div>
    );
  }
  
  return (
    <div className="flex items-center justify-center">
      <span className="inline-flex items-center justify-center w-6 h-6 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium">
        {rank}
      </span>
    </div>
  );
};
