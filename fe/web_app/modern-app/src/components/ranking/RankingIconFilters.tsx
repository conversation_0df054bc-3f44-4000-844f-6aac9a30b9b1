import React from 'react';
import { useTranslation } from 'react-i18next';
import { RankingFilter } from '../../types';

interface RankingIconFiltersProps {
  filter: RankingFilter;
  onFilterChange: (filter: RankingFilter) => void;
  disabled?: boolean;
}

export const RankingIconFilters: React.FC<RankingIconFiltersProps> = ({
  filter,
  onFilterChange,
  disabled = false
}) => {
  const { t } = useTranslation('common');
  
  const handleJobChange = (job: string) => {
    onFilterChange({
      ...filter,
      job: job as RankingFilter['job'],
      page: 1, // 重置到第一页
    });
  };

  const handleSortChange = (sort: string) => {
    onFilterChange({
      ...filter,
      sort: sort as RankingFilter['sort'],
      page: 1, // 重置到第一页
    });
  };

  const jobFilters = [
    { value: 'all', icon: '/static/images/all.png', title: t('ranking.filters.all') },
    { value: 'beginner', icon: '/static/images/beginner.png', title: t('ranking.filters.beginner') },
    { value: 'warrior', icon: '/static/images/warrior.png', title: t('ranking.filters.warrior') },
    { value: 'magician', icon: '/static/images/magician.png', title: t('ranking.filters.magician') },
    { value: 'bowman', icon: '/static/images/bowman.png', title: t('ranking.filters.bowman') },
    { value: 'thief', icon: '/static/images/thief.png', title: t('ranking.filters.thief') },
    { value: 'pirate', icon: '/static/images/pirate.png', title: t('ranking.filters.pirate') },
    { value: 'cygnus', icon: '/static/images/cygnus.png', title: t('ranking.filters.cygnus') },
    { value: 'aran', icon: '/static/images/aran.png', title: t('ranking.filters.aran') },
  ];

  const sortFilters = [
    { value: 'fame', icon: '/static/images/fame.png', title: t('ranking.tabs.fame') },
    { value: 'quest', icon: '/static/images/quest.png', title: t('ranking.tabs.quest') },
    { value: 'monsterbook', icon: '/static/images/monsterbook.png', title: t('ranking.tabs.monsterbook') },
    { value: 'guild', icon: '/static/images/guild.png', title: t('ranking.tabs.guild') },
  ];

  return (
    <div className="mb-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex flex-wrap gap-2 items-center justify-center">
          {/* 职业筛选 */}
          {jobFilters.map((jobFilter) => (
            <button
              key={jobFilter.value}
              onClick={() => !disabled && handleJobChange(jobFilter.value)}
              disabled={disabled}
              className={`p-2 rounded-lg transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700 ${
                filter.job === jobFilter.value || (filter.job === undefined && jobFilter.value === 'all')
                  ? 'bg-blue-100 dark:bg-blue-900 ring-2 ring-blue-500'
                  : 'bg-gray-50 dark:bg-gray-700'
              } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
              title={jobFilter.title}
            >
              <img
                src={jobFilter.icon}
                alt={jobFilter.title}
                className="w-8 h-8"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                }}
              />
            </button>
          ))}
          
          {/* 分隔线 */}
          <div className="w-px h-8 bg-gray-300 dark:bg-gray-600 mx-2" />
          
          {/* 排序筛选 */}
          {sortFilters.map((sortFilter) => (
            <button
              key={sortFilter.value}
              onClick={() => !disabled && handleSortChange(sortFilter.value)}
              disabled={disabled}
              className={`p-2 rounded-lg transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700 ${
                filter.sort === sortFilter.value
                  ? 'bg-green-100 dark:bg-green-900 ring-2 ring-green-500'
                  : 'bg-gray-50 dark:bg-gray-700'
              } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
              title={sortFilter.title}
            >
              <img
                src={sortFilter.icon}
                alt={sortFilter.title}
                className="w-8 h-8"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                }}
              />
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};
