import React from 'react';
import { Monitor, Smartphone } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent } from '../ui/Card';

interface ClientPreviewProps {
  className?: string;
}

export const ClientPreview: React.FC<ClientPreviewProps> = ({ className }) => {
  const { t } = useTranslation();

  return (
    <Card className={className}>
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* 客户端预览图 */}
          <div className="relative bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg p-8 text-white text-center">
            <div className="space-y-4">
              <div className="flex justify-center space-x-4">
                <Monitor className="h-12 w-12" />
                <Smartphone className="h-12 w-12" />
              </div>
              <div>
                <h3 className="text-xl font-bold">MagicMS Client</h3>
                <p className="text-blue-100">
                  {t('common:download.clientInfo.contains')}
                </p>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="bg-white/10 rounded p-2">
                  <p className="font-medium">HD Client</p>
                  <p className="text-blue-100">1280x720</p>
                </div>
                <div className="bg-white/10 rounded p-2">
                  <p className="font-medium">Normal Client</p>
                  <p className="text-blue-100">800x600</p>
                </div>
              </div>
            </div>
          </div>
          
          {/* 版本信息 */}
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <p className="text-sm text-muted-foreground">{t('common:download.clientInfo.version')}</p>
              <p className="font-bold text-lg">v1.25</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">{t('common:download.clientInfo.size')}</p>
              <p className="font-bold text-lg">~2.5GB</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">{t('common:download.clientInfo.updateDate')}</p>
              <p className="font-bold text-lg">01-25</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
