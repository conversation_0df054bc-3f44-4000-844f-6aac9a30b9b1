import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import ReactMarkdown from 'react-markdown';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/Dialog';
import { Button } from '../ui/Button';
import { ScrollArea } from '../ui/ScrollArea';
import { Checkbox } from '../ui/Checkbox';
import { FormLabel } from '../ui/Form';
import { AuthService } from '../../api/services/auth';

interface TermsOfServiceModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAccept: () => void;
}

const TermsOfServiceModal: React.FC<TermsOfServiceModalProps> = ({
  open,
  onOpenChange,
  onAccept,
}) => {
  const { t } = useTranslation();
  const [hasScrolledToBottom, setHasScrolledToBottom] = useState(false);
  const [hasReadTerms, setHasReadTerms] = useState(false);
  const [readingProgress, setReadingProgress] = useState(0);
  const [tosContent, setTosContent] = useState<string>('');
  const [tosTitle, setTosTitle] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 重置状态当模态框打开时
  useEffect(() => {
    if (open) {
      setHasScrolledToBottom(false);
      setHasReadTerms(false);
      setReadingProgress(0);
    }
  }, [open]);

  // 获取TOS内容
  useEffect(() => {
    if (open && !tosContent) {
      const fetchTosContent = async () => {
        try {
          setLoading(true);
          setError(null);
          const response = await AuthService.getTermsOfService();
          setTosContent(response.content || '');
          setTosTitle(response.title || t('auth.register.tosTitle'));
        } catch (err) {
          console.error('Failed to fetch TOS content:', err);
          setError(t('auth.register.tosLoadError'));
          // 使用默认内容作为后备
          setTosTitle(t('auth.register.tosTitle'));
          setTosContent(getDefaultTosContent());
        } finally {
          setLoading(false);
        }
      };

      fetchTosContent();
    }
  }, [open, tosContent, t]);

  // 默认服务条款内容（作为后备）
  const getDefaultTosContent = () => `# 服务条款

## 1. 服务说明

欢迎使用我们的MapleStory私服务器。在使用我们的服务之前，请仔细阅读以下条款。

## 2. 用户责任

- 用户必须遵守游戏规则和社区准则
- 禁止使用外挂、作弊软件或其他违规工具
- 禁止恶意攻击服务器或其他玩家
- 禁止发布不当言论或内容

## 3. 账户安全

- 用户有责任保护自己的账户安全
- 不得与他人共享账户信息
- 如发现账户异常，请及时联系管理员

## 4. 服务变更

- 我们保留随时修改或终止服务的权利
- 重要变更将提前通知用户

## 5. 免责声明

- 服务按"现状"提供，不提供任何明示或暗示的保证
- 我们不对因使用服务而产生的任何损失承担责任

## 6. 联系我们

如有任何问题，请通过游戏内邮件或官方论坛联系我们。

最后更新：2024年1月1日`;

  // 处理滚动事件
  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = event.currentTarget;
    const scrollPercentage = (scrollTop / (scrollHeight - clientHeight)) * 100;
    
    setReadingProgress(Math.min(scrollPercentage, 100));
    
    // 检查是否滚动到底部（允许5px的误差）
    if (scrollHeight - scrollTop - clientHeight < 5) {
      setHasScrolledToBottom(true);
    }
  };

  const handleAccept = () => {
    if (hasScrolledToBottom && hasReadTerms) {
      onAccept();
      onOpenChange(false);
    }
  };



  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>{tosTitle || t('auth.register.termsTitle')}</DialogTitle>
          <DialogDescription>
            {t('auth.register.termsDescription')}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 阅读进度条 */}
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-primary h-2 rounded-full transition-all duration-300"
              style={{ width: `${readingProgress}%` }}
            />
          </div>
          <p className="text-sm text-muted-foreground">
            {t('auth.register.readingProgress')}: {Math.round(readingProgress)}%
          </p>

          {/* 服务条款内容 */}
          <ScrollArea className="h-96 w-full border rounded-md p-4" onScrollCapture={handleScroll}>
            <div className="prose prose-sm max-w-none">
              {loading ? (
                <div className="flex items-center justify-center h-32">
                  <div className="text-sm text-muted-foreground">
                    {t('auth.register.loadingTos')}...
                  </div>
                </div>
              ) : error ? (
                <div className="text-sm text-red-600 p-4 bg-red-50 rounded">
                  <p className="font-medium">{t('auth.register.tosLoadError')}</p>
                  <p className="mt-2">{error}</p>
                  <p className="mt-2 text-xs">使用默认服务条款内容</p>
                </div>
              ) : (
                <ReactMarkdown
                  className="text-sm leading-relaxed"
                  components={{
                    h1: ({children}) => <h1 className="text-lg font-bold mb-4 text-gray-900">{children}</h1>,
                    h2: ({children}) => <h2 className="text-base font-semibold mb-3 mt-6 text-gray-800">{children}</h2>,
                    h3: ({children}) => <h3 className="text-sm font-medium mb-2 mt-4 text-gray-700">{children}</h3>,
                    p: ({children}) => <p className="mb-3 text-gray-600 leading-relaxed">{children}</p>,
                    ul: ({children}) => <ul className="mb-3 ml-4 space-y-1">{children}</ul>,
                    li: ({children}) => <li className="text-gray-600 text-sm list-disc">{children}</li>,
                    strong: ({children}) => <strong className="font-medium text-gray-800">{children}</strong>,
                    em: ({children}) => <em className="italic text-gray-700">{children}</em>,
                    hr: () => <hr className="my-6 border-gray-200" />,
                  }}
                >
                  {tosContent || getDefaultTosContent()}
                </ReactMarkdown>
              )}
            </div>
          </ScrollArea>

          {/* 确认阅读复选框 */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="readTerms"
              checked={hasReadTerms}
              onCheckedChange={(checked) => setHasReadTerms(checked as boolean)}
              disabled={!hasScrolledToBottom}
            />
            <FormLabel htmlFor="readTerms" className="text-sm">
              {hasScrolledToBottom 
                ? t('auth.register.confirmRead')
                : t('auth.register.mustScrollToBottom')
              }
            </FormLabel>
          </div>

          {!hasScrolledToBottom && (
            <p className="text-sm text-amber-600">
              {t('auth.register.scrollToBottomHint')}
            </p>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            {t('common.cancel')}
          </Button>
          <Button 
            onClick={handleAccept}
            disabled={!hasScrolledToBottom || !hasReadTerms}
          >
            {t('auth.register.acceptTerms')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default TermsOfServiceModal;
