import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import ReactMarkdown from 'react-markdown';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/Dialog';
import { Button } from '../ui/Button';
import { ScrollArea } from '../ui/ScrollArea';
import { Checkbox } from '../ui/Checkbox';
import { FormLabel } from '../ui/Form';
import { AuthService } from '../../api/services/auth';

interface TermsOfServiceModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAccept: () => void;
}

const TermsOfServiceModal: React.FC<TermsOfServiceModalProps> = ({
  open,
  onOpenChange,
  onAccept,
}) => {
  const { t } = useTranslation();
  const [hasScrolledToBottom, setHasScrolledToBottom] = useState(false);
  const [hasReadTerms, setHasReadTerms] = useState(false);
  const [readingProgress, setReadingProgress] = useState(0);
  const [tosContent, setTosContent] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 重置状态当模态框打开时
  useEffect(() => {
    if (open) {
      setHasScrolledToBottom(false);
      setHasReadTerms(false);
      setReadingProgress(0);
    }
  }, [open]);

  // 获取TOS内容
  useEffect(() => {
    if (open && !tosContent) {
      const fetchTosContent = async () => {
        try {
          setLoading(true);
          setError(null);
          const response = await AuthService.getTermsOfService();
          setTosContent(response.content || '');
        } catch (err) {
          console.error('Failed to fetch TOS content:', err);
          setError(t('auth.register.tosLoadError'));
          // 使用默认内容作为后备
          setTosContent(`# ${t('auth.register.tosTitle')}\n\n${t('auth.register.tosDefaultContent')}`);
        } finally {
          setLoading(false);
        }
      };

      fetchTosContent();
    }
  }, [open, tosContent, t]);

  // 处理滚动事件
  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = event.currentTarget;
    const scrollPercentage = (scrollTop / (scrollHeight - clientHeight)) * 100;
    
    setReadingProgress(Math.min(scrollPercentage, 100));
    
    // 检查是否滚动到底部（允许5px的误差）
    if (scrollHeight - scrollTop - clientHeight < 5) {
      setHasScrolledToBottom(true);
    }
  };

  const handleAccept = () => {
    if (hasScrolledToBottom && hasReadTerms) {
      onAccept();
      onOpenChange(false);
    }
  };



  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>{t('auth.register.termsTitle')}</DialogTitle>
          <DialogDescription>
            {t('auth.register.termsDescription')}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 阅读进度条 */}
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-primary h-2 rounded-full transition-all duration-300"
              style={{ width: `${readingProgress}%` }}
            />
          </div>
          <p className="text-sm text-muted-foreground">
            {t('auth.register.readingProgress')}: {Math.round(readingProgress)}%
          </p>

          {/* 服务条款内容 */}
          <ScrollArea className="h-96 w-full border rounded-md p-4" onScrollCapture={handleScroll}>
            <div className="prose prose-sm max-w-none">
              {loading ? (
                <div className="flex items-center justify-center h-32">
                  <div className="text-sm text-muted-foreground">
                    {t('auth.register.loadingTos')}...
                  </div>
                </div>
              ) : error ? (
                <div className="text-sm text-red-600 p-4 bg-red-50 rounded">
                  <p className="font-medium">{t('auth.register.tosLoadError')}</p>
                  <p className="mt-2">{error}</p>
                </div>
              ) : (
                <ReactMarkdown
                  className="text-sm leading-relaxed"
                  components={{
                    h1: ({children}) => <h1 className="text-lg font-bold mb-4 text-gray-900">{children}</h1>,
                    h2: ({children}) => <h2 className="text-base font-semibold mb-3 mt-6 text-gray-800">{children}</h2>,
                    h3: ({children}) => <h3 className="text-sm font-medium mb-2 mt-4 text-gray-700">{children}</h3>,
                    p: ({children}) => <p className="mb-3 text-gray-600 leading-relaxed">{children}</p>,
                    ul: ({children}) => <ul className="mb-3 ml-4 space-y-1">{children}</ul>,
                    li: ({children}) => <li className="text-gray-600 text-sm list-disc">{children}</li>,
                    strong: ({children}) => <strong className="font-medium text-gray-800">{children}</strong>,
                    em: ({children}) => <em className="italic text-gray-700">{children}</em>,
                    hr: () => <hr className="my-6 border-gray-200" />,
                  }}
                >
                  {tosContent || t('auth.register.tosDefaultContent')}
                </ReactMarkdown>
              )}
            </div>
          </ScrollArea>

          {/* 确认阅读复选框 */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="readTerms"
              checked={hasReadTerms}
              onCheckedChange={(checked) => setHasReadTerms(checked as boolean)}
              disabled={!hasScrolledToBottom}
            />
            <FormLabel htmlFor="readTerms" className="text-sm">
              {hasScrolledToBottom 
                ? t('auth.register.confirmRead')
                : t('auth.register.mustScrollToBottom')
              }
            </FormLabel>
          </div>

          {!hasScrolledToBottom && (
            <p className="text-sm text-amber-600">
              {t('auth.register.scrollToBottomHint')}
            </p>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            {t('common.cancel')}
          </Button>
          <Button 
            onClick={handleAccept}
            disabled={!hasScrolledToBottom || !hasReadTerms}
          >
            {t('auth.register.acceptTerms')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default TermsOfServiceModal;
