import { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';

interface ScrollPosition {
  x: number;
  y: number;
}

// 全局存储滚动位置
const scrollPositions = new Map<string, ScrollPosition>();

export const useScrollPosition = (key?: string) => {
  const location = useLocation();
  const scrollKey = key || location.pathname;
  const isRestoringRef = useRef(false);

  // 保存当前滚动位置
  const saveScrollPosition = () => {
    if (!isRestoringRef.current) {
      scrollPositions.set(scrollKey, {
        x: window.scrollX,
        y: window.scrollY
      });
    }
  };

  // 恢复滚动位置
  const restoreScrollPosition = () => {
    const savedPosition = scrollPositions.get(scrollKey);
    if (savedPosition) {
      isRestoringRef.current = true;
      window.scrollTo(savedPosition.x, savedPosition.y);
      // 延迟重置标志，确保滚动完成
      setTimeout(() => {
        isRestoringRef.current = false;
      }, 100);
    }
  };

  // 清除指定路径的滚动位置
  const clearScrollPosition = (pathKey?: string) => {
    const keyToClear = pathKey || scrollKey;
    scrollPositions.delete(keyToClear);
  };

  // 监听滚动事件，保存位置
  useEffect(() => {
    const handleScroll = () => {
      saveScrollPosition();
    };

    // 添加滚动监听
    window.addEventListener('scroll', handleScroll, { passive: true });

    // 页面卸载时保存位置
    const handleBeforeUnload = () => {
      saveScrollPosition();
    };
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [scrollKey]);

  return {
    saveScrollPosition,
    restoreScrollPosition,
    clearScrollPosition
  };
};

// 用于页面组件的滚动位置恢复hook
export const useScrollRestore = (key?: string) => {
  const { restoreScrollPosition } = useScrollPosition(key);

  useEffect(() => {
    // 页面加载完成后恢复滚动位置
    const timer = setTimeout(() => {
      restoreScrollPosition();
    }, 100);

    return () => clearTimeout(timer);
  }, [restoreScrollPosition]);
};
