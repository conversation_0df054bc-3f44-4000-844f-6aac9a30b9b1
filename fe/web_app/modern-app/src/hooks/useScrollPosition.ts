import { useEffect, useRef, useCallback } from 'react';
import { useLocation } from 'react-router-dom';

interface ScrollPosition {
  x: number;
  y: number;
}

// 全局存储滚动位置
const scrollPositions = new Map<string, ScrollPosition>();

export const useScrollPosition = (key?: string) => {
  const location = useLocation();
  const scrollKey = key || location.pathname;
  const isRestoringRef = useRef(false);

  // 保存当前滚动位置
  const saveScrollPosition = () => {
    if (!isRestoringRef.current) {
      scrollPositions.set(scrollKey, {
        x: window.scrollX,
        y: window.scrollY
      });
    }
  };

  // 恢复滚动位置
  const restoreScrollPosition = () => {
    const savedPosition = scrollPositions.get(scrollKey);
    if (savedPosition) {
      isRestoringRef.current = true;
      window.scrollTo(savedPosition.x, savedPosition.y);
      // 延迟重置标志，确保滚动完成
      setTimeout(() => {
        isRestoringRef.current = false;
      }, 100);
    }
  };

  // 清除指定路径的滚动位置
  const clearScrollPosition = (pathKey?: string) => {
    const keyToClear = pathKey || scrollKey;
    scrollPositions.delete(keyToClear);
  };

  // 监听滚动事件，保存位置
  useEffect(() => {
    const handleScroll = () => {
      saveScrollPosition();
    };

    // 添加滚动监听
    window.addEventListener('scroll', handleScroll, { passive: true });

    // 页面卸载时保存位置
    const handleBeforeUnload = () => {
      saveScrollPosition();
    };
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [scrollKey]);

  return {
    saveScrollPosition,
    restoreScrollPosition,
    clearScrollPosition
  };
};

// 用于页面组件的滚动位置恢复hook
export const useScrollRestore = (key?: string) => {
  const { restoreScrollPosition } = useScrollPosition(key);

  useEffect(() => {
    // 页面加载完成后恢复滚动位置
    const timer = setTimeout(() => {
      restoreScrollPosition();
    }, 300); // 增加延迟，确保内容加载完成

    return () => clearTimeout(timer);
  }, [restoreScrollPosition]);
};

// 专门用于列表页面的滚动位置管理
export const useListScrollPosition = (listKey: string) => {
  const location = useLocation();
  const { saveScrollPosition, restoreScrollPosition, clearScrollPosition } = useScrollPosition(listKey);

  // 保存列表位置（在进入详情页前调用）
  const saveListPosition = useCallback(() => {
    console.log('Saving list position for:', listKey);
    saveScrollPosition();
  }, [saveScrollPosition, listKey]);

  // 恢复列表位置（从详情页返回时调用）
  const restoreListPosition = useCallback(() => {
    // 只有当前在列表页面时才恢复位置
    if (location.pathname === listKey) {
      console.log('Restoring list position for:', listKey);
      // 延迟恢复，确保列表内容已加载
      setTimeout(() => {
        restoreScrollPosition();
      }, 800); // 增加延迟时间
    }
  }, [restoreScrollPosition, location.pathname, listKey]);

  return {
    saveListPosition,
    restoreListPosition,
    clearScrollPosition
  };
};
