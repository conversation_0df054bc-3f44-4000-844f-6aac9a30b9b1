import { useState, useEffect, useCallback } from 'react';
import { ServerStatus } from '../types';
import { serverStatusManager } from '../utils/serverStatusManager';

export const useServerStatus = () => {
  const [status, setStatus] = useState<ServerStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 初始化时获取当前状态
  useEffect(() => {
    const currentState = serverStatusManager.getCurrentStatus();
    setStatus(currentState.status);
    setLoading(currentState.loading);
    setError(currentState.error);

    // 如果没有状态，触发首次获取
    if (!currentState.status && !currentState.loading) {
      serverStatusManager.fetchStatus();
    }

    // 添加状态监听器
    const unsubscribe = serverStatusManager.addListener((newStatus, newLoading, newError) => {
      setStatus(newStatus);
      setLoading(newLoading);
      setError(newError);
    });

    return unsubscribe;
  }, []);

  // 获取服务器状态
  const fetchServerStatus = useCallback(async (forceRefresh = false) => {
    return serverStatusManager.fetchStatus(forceRefresh);
  }, []);

  // 检查是否启用邀请码注册
  const isInviteRequired = useCallback((): boolean => {
    return serverStatusManager.isInviteRequired();
  }, []);

  // 强制刷新函数
  const refresh = useCallback(() => {
    return fetchServerStatus(true);
  }, [fetchServerStatus]);

  return {
    status,
    loading,
    error,
    fetchServerStatus,
    isInviteRequired,
    refresh,
  };
};
