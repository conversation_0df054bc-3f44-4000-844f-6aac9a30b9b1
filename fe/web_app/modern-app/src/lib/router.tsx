import { createBrowserRouter, Navigate } from 'react-router-dom';
import { lazy, Suspense } from 'react';

// 布局组件
import RootLayout from '../components/layout/RootLayout';
import LoadingSpinner from '../components/common/LoadingSpinner';

// 懒加载页面组件
const HomePage = lazy(() => import('../pages/home/<USER>'));
const DownloadPage = lazy(() => import('../pages/download/DownloadPage'));
const RankingPage = lazy(() => import('../pages/ranking/RankingPage'));
const ShopPage = lazy(() => import('../pages/shop/ShopPage'));
const LibraryPage = lazy(() => import('../pages/library/LibraryPage'));
const VotePage = lazy(() => import('../pages/vote/VotePage'));
const NoticePage = lazy(() => import('../pages/notice/NoticePage'));
const NoticeDetailPage = lazy(() => import('../pages/notice/NoticeDetailPage'));
const RegisterPage = lazy(() => import('../pages/auth/RegisterPage'));
const LoginPage = lazy(() => import('../pages/auth/LoginPage'));
const ForgotPasswordPage = lazy(() => import('../pages/auth/ForgotPasswordPage'));
const ProfilePage = lazy(() => import('../pages/profile/ProfilePage'));
const NotFoundPage = lazy(() => import('../pages/error/NotFoundPage'));

// 包装懒加载组件的高阶组件
const withSuspense = (Component: React.ComponentType) => {
  return (props: any) => (
    <Suspense fallback={<LoadingSpinner />}>
      <Component {...props} />
    </Suspense>
  );
};

// 路由配置
export const router = createBrowserRouter([
  {
    path: '/',
    element: <RootLayout />,
    errorElement: withSuspense(NotFoundPage)({}),
    children: [
      {
        index: true,
        element: withSuspense(HomePage)({}),
      },
      {
        path: 'download',
        element: withSuspense(DownloadPage)({}),
      },
      {
        path: 'ranking',
        element: withSuspense(RankingPage)({}),
      },
      {
        path: 'shop',
        element: withSuspense(ShopPage)({}),
      },
      {
        path: 'library',
        element: withSuspense(LibraryPage)({}),
      },
      {
        path: 'vote',
        element: withSuspense(VotePage)({}),
      },
      {
        path: 'notice',
        children: [
          {
            index: true,
            element: withSuspense(NoticePage)({}),
          },
          {
            path: ':id',
            element: withSuspense(NoticeDetailPage)({}),
          },
        ],
      },
      {
        path: 'auth',
        children: [
          {
            path: 'register',
            element: withSuspense(RegisterPage)({}),
          },
          {
            path: 'login',
            element: withSuspense(LoginPage)({}),
          },
          {
            path: 'forgot-password',
            element: withSuspense(ForgotPasswordPage)({}),
          },
        ],
      },
      {
        path: 'profile',
        element: withSuspense(ProfilePage)({}),
      },
      // 兼容旧路由
      {
        path: 'register',
        element: <Navigate to="/auth/register" replace />,
      },
      {
        path: 'login',
        element: <Navigate to="/auth/login" replace />,
      },
      {
        path: 'forgot',
        element: <Navigate to="/auth/forgot-password" replace />,
      },
      {
        path: 'cashshop',
        element: <Navigate to="/shop" replace />,
      },
      {
        path: 'wz',
        element: <Navigate to="/library" replace />,
      },
      {
        path: 'ranking',
        element: <Navigate to="/ranking" replace />,
      },
    ],
  },
]);

// 路由常量
export const ROUTES = {
  HOME: '/',
  DOWNLOAD: '/download',
  RANKING: '/ranking',
  SHOP: '/shop',
  LIBRARY: '/library',
  VOTE: '/vote',
  NOTICE: '/notice',
  NOTICE_DETAIL: (id: string | number) => `/notice/${id}`,
  AUTH: {
    REGISTER: '/auth/register',
    LOGIN: '/auth/login',
    FORGOT_PASSWORD: '/auth/forgot-password',
  },
  PROFILE: '/profile',
} as const;
