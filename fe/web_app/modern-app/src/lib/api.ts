import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ApiResponse } from '../types';

// API配置
const API_CONFIG = {
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api/v1',
  timeout: 10000,
};

// 创建axios实例
const apiClient: AxiosInstance = axios.create(API_CONFIG);

// Token存储工具
class TokenStorage {
  private static readonly TOKEN_KEY = 'token';
  private static readonly REFRESH_TOKEN_KEY = 'refresh_token';

  static setToken(token: string, expiresIn: number): void {
    const expireTime = Date.now() + expiresIn * 1000;
    localStorage.setItem(this.TOKEN_KEY, token);
    localStorage.setItem(`${this.TOKEN_KEY}_expire`, expireTime.toString());
  }

  static setRefreshToken(refreshToken: string, expiresIn: number): void {
    const expireTime = Date.now() + expiresIn * 1000;
    localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
    localStorage.setItem(`${this.REFRESH_TOKEN_KEY}_expire`, expireTime.toString());
  }

  static getToken(): string | null {
    const token = localStorage.getItem(this.TOKEN_KEY);
    const expireTime = localStorage.getItem(`${this.TOKEN_KEY}_expire`);
    
    if (!token || !expireTime) return null;
    
    if (Date.now() > parseInt(expireTime)) {
      this.clearToken();
      return null;
    }
    
    return token;
  }

  static getRefreshToken(): string | null {
    const refreshToken = localStorage.getItem(this.REFRESH_TOKEN_KEY);
    const expireTime = localStorage.getItem(`${this.REFRESH_TOKEN_KEY}_expire`);
    
    if (!refreshToken || !expireTime) return null;
    
    if (Date.now() > parseInt(expireTime)) {
      this.clearRefreshToken();
      return null;
    }
    
    return refreshToken;
  }

  static clearToken(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(`${this.TOKEN_KEY}_expire`);
  }

  static clearRefreshToken(): void {
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    localStorage.removeItem(`${this.REFRESH_TOKEN_KEY}_expire`);
  }

  static clearAll(): void {
    this.clearToken();
    this.clearRefreshToken();
  }
}

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    const token = TokenStorage.getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // 如果是401错误且还没有重试过
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      const refreshToken = TokenStorage.getRefreshToken();
      if (refreshToken) {
        try {
          // 尝试刷新token
          const response = await axios.post(`${API_CONFIG.baseURL}/auth/refresh`, {
            refresh_token: refreshToken,
          });

          const { token, expires_in } = response.data;
          TokenStorage.setToken(token, expires_in);

          // 重新发送原请求
          originalRequest.headers.Authorization = `Bearer ${token}`;
          return apiClient(originalRequest);
        } catch (refreshError) {
          // 刷新失败，清除所有token并跳转到登录页
          TokenStorage.clearAll();
          window.location.href = '/login';
          return Promise.reject(refreshError);
        }
      } else {
        // 没有refresh token，直接跳转到登录页
        TokenStorage.clearAll();
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

// API请求封装类
export class ApiService {
  static async get<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await apiClient.get<ApiResponse<T>>(url, config);
    return response.data;
  }

  static async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await apiClient.post<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  static async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await apiClient.put<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  static async delete<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await apiClient.delete<ApiResponse<T>>(url, config);
    return response.data;
  }

  static async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await apiClient.patch<ApiResponse<T>>(url, data, config);
    return response.data;
  }
}

// 导出token管理工具
export { TokenStorage };

// 导出axios实例（用于特殊情况）
export { apiClient };
