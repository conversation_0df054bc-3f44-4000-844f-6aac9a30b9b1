import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// 翻译资源
const resources = {
  zh: {
    translation: {
      nav: {
        home: '首页',
        download: '下载',
        ranking: '排行榜',
        shop: '商城',
        library: '资料库',
        vote: '投票',
        notice: '公告',
      },
      auth: {
        login: {
          title: '登录',
          description: '登录您的账号',
        },
        register: {
          title: '注册',
          description: '创建新账号',
        },
      },
      profile: {
        language: '切换语言',
      },
      welcome: {
        title: '欢迎来到 MagicMS',
      },
    },
  },
  en: {
    translation: {
      nav: {
        home: 'Home',
        download: 'Download',
        ranking: 'Ranking',
        shop: 'Shop',
        library: 'Library',
        vote: 'Vote',
        notice: 'Notice',
      },
      auth: {
        login: {
          title: 'Login',
          description: 'Sign in to your account',
        },
        register: {
          title: 'Register',
          description: 'Create a new account',
        },
      },
      profile: {
        language: 'Switch Language',
      },
      welcome: {
        title: 'Welcome to MagicMS',
      },
    },
  },
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'zh',
    debug: false,
    interpolation: {
      escapeValue: false,
    },
    detection: {
      order: ['localStorage', 'navigator'],
      caches: ['localStorage'],
    },
  });

export default i18n;
