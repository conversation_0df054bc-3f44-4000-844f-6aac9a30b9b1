import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// 直接定义翻译资源（临时测试）
const zhCommon = {
  nav: {
    home: "首页",
    download: "下载",
    ranking: "排行榜",
    shop: "商城",
    library: "资料库",
    vote: "投票",
    notice: "公告",
    profile: "个人资料",
    demo: "组件演示",
    login: "登录",
    register: "注册",
    logout: "退出登录"
  },
  home: {
    welcome: "欢迎来到 MagicMS",
    description: "体验最纯正的冒险岛私服，与全球玩家一起踏上奇幻冒险之旅",
    downloadNow: "立即下载",
    registerAccount: "注册账号",
    serverInfo: {
      title: "服务器信息",
      description: "实时服务器状态和倍率信息",
      expRate: "经验倍率",
      mesoRate: "金币倍率",
      dropRate: "掉落倍率",
      bossRate: "BOSS倍率"
    },
    features: {
      title: "核心功能",
      description: "探索MagicMS的精彩功能",
      quickDownload: {
        title: "快速下载",
        description: "一键下载游戏客户端，快速开始游戏"
      },
      ranking: {
        title: "排行榜",
        description: "查看角色和公会排行榜，展示你的实力"
      },
      shop: {
        title: "商城",
        description: "购买游戏道具，提升游戏体验"
      },
      community: {
        title: "社区",
        description: "加入我们的社区，与其他玩家交流"
      }
    },
    gameFeatures: {
      title: "游戏特色",
      description: "独特的游戏机制和自定义功能",
      coreFeatures: "核心特色",
      jobEnhancements: "职业增强"
    },
    rankings: {
      title: "排行榜",
      description: "查看服务器顶级玩家"
    }
  },
  profile: {
    title: "个人资料",
    language: "切换语言",
    settings: "设置",
    logout: "退出登录"
  }
};

const enCommon = {
  nav: {
    home: "Home",
    download: "Download",
    ranking: "Ranking",
    shop: "Shop",
    library: "Library",
    vote: "Vote",
    notice: "Notice",
    profile: "Profile",
    demo: "Components Demo",
    login: "Login",
    register: "Register",
    logout: "Logout"
  },
  home: {
    welcome: "Welcome to MagicMS",
    description: "Experience the purest MapleStory private server and embark on a magical adventure with players worldwide",
    downloadNow: "Download Now",
    registerAccount: "Register Account",
    serverInfo: {
      title: "Server Information",
      description: "Real-time server status and rate information",
      expRate: "EXP Rate",
      mesoRate: "Meso Rate",
      dropRate: "Drop Rate",
      bossRate: "Boss Rate"
    },
    features: {
      title: "Core Features",
      description: "Explore the exciting features of MagicMS",
      quickDownload: {
        title: "Quick Download",
        description: "One-click download of game client, start playing quickly"
      },
      ranking: {
        title: "Rankings",
        description: "View character and guild rankings, showcase your strength"
      },
      shop: {
        title: "Shop",
        description: "Purchase game items to enhance your gaming experience"
      },
      community: {
        title: "Community",
        description: "Join our community and interact with other players"
      }
    },
    gameFeatures: {
      title: "Game Features",
      description: "Unique game mechanics and custom features",
      coreFeatures: "Core Features",
      jobEnhancements: "Job Enhancements"
    },
    rankings: {
      title: "Rankings",
      description: "View top players on the server"
    }
  },
  profile: {
    title: "Profile",
    language: "Switch Language",
    settings: "Settings",
    logout: "Logout"
  }
};

// 翻译资源配置
const resources = {
  zh: {
    common: zhCommon,
  },
  en: {
    common: enCommon,
  },
};

// 初始化 i18n
i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'zh',
    defaultNS: 'common',
    ns: ['common'],
    debug: process.env.NODE_ENV === 'development',

    interpolation: {
      escapeValue: false, // React 已经处理了 XSS
    },

    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      lookupLocalStorage: 'preferred-language',
      caches: ['localStorage'],
    },

    react: {
      useSuspense: false, // 避免 SSR 问题
    },
  });

// 导出语言切换函数
export const changeLanguage = (lng: string) => {
  return i18n.changeLanguage(lng);
};

// 导出当前语言
export const getCurrentLanguage = () => {
  return i18n.language;
};

// 导出支持的语言列表
export const supportedLanguages = ['zh', 'en'] as const;
export type SupportedLanguage = typeof supportedLanguages[number];

export default i18n;
