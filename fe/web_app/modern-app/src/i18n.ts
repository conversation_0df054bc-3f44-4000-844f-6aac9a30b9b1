import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// 导入翻译资源
import zhCommon from './locales/zh/common.json';
import enCommon from './locales/en/common.json';
import zhProfile from './i18n/locales/zh/profile.json';
import enProfile from './i18n/locales/en/profile.json';

// 翻译资源配置
const resources = {
  zh: {
    common: zhCommon,
    profile: zhProfile,
  },
  en: {
    common: enCommon,
    profile: enProfile,
  },
};

// 初始化 i18n
i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'zh',
    defaultNS: 'common',
    ns: ['common', 'profile'],
    debug: process.env.NODE_ENV === 'development',

    interpolation: {
      escapeValue: false, // React 已经处理了 XSS
    },

    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      lookupLocalStorage: 'preferred-language',
      caches: ['localStorage'],
    },

    react: {
      useSuspense: false, // 避免 SSR 问题
    },
  });

// 导出语言切换函数
export const changeLanguage = (lng: string) => {
  return i18n.changeLanguage(lng);
};

// 导出当前语言
export const getCurrentLanguage = () => {
  return i18n.language;
};

// 导出支持的语言列表
export const supportedLanguages = ['zh', 'en'] as const;
export type SupportedLanguage = typeof supportedLanguages[number];

export default i18n;
