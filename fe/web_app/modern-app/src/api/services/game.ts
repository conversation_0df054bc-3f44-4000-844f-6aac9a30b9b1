import { request } from '../client';
import { 
  ServerStatus, 
  Character, 
  RankingItem, 
  RankingFilter, 
  PaginatedResponse 
} from '../../types';

export class GameService {
  /**
   * 获取服务器状态
   */
  static async getServerStatus(): Promise<ServerStatus> {
    const response = await request.get<any>('/v1/game/status');
    const data = response.data;

    // 转换旧API格式到新格式
    return {
      isOnline: data.status === '正常',
      status: data.status,
      onlinePlayers: data.count || 0,
      count: data.count || 0,
      invite: data.invite === true,
      expRate: data.exp_rate || 1,
      mesoRate: data.meso_rate || 1,
      dropRate: data.drop_rate || 1,
      bossRate: data.boss_rate || 1,
      serverTime: data.server_time,
      version: data.version,
    };
  }

  /**
   * 获取角色列表
   */
  static async getCharacterList(): Promise<Character[]> {
    const response = await request.get<Character[]>('/character/list');
    return response.data;
  }

  /**
   * 每日签到
   */
  static async checkIn(characterId: number): Promise<{ reward: string; nextReward?: string }> {
    const response = await request.post<{ reward: string; nextReward?: string }>('/user/checkin', {
      character_id: characterId,
    });
    return response.data;
  }

  /**
   * 游戏解卡
   */
  static async unlockAccount(): Promise<{ message: string }> {
    const response = await request.post<{ message: string }>('/game/ea');
    return response.data;
  }

  /**
   * 获取角色排行榜
   */
  static async getCharacterRanking(filter: RankingFilter = {}): Promise<RankingResponse> {
    const params = new URLSearchParams();

    if (filter.job && filter.job !== 'all') params.append('job', filter.job);
    if (filter.sort) params.append('sort', filter.sort);
    if (filter.page) params.append('page', filter.page.toString());
    if (filter.limit) params.append('size', filter.limit.toString()); // 注意：后端使用 'size' 参数

    try {
      // 使用旧版API路径以保持兼容性
      const response = await request.get<{
        items: any[];
        total: number;
        page: number;
        size: number;
      }>(`/v1/game/character/rank?${params.toString()}`);

      // 转换数据格式以匹配新的类型定义
      const transformedItems: RankingItem[] = response.data.items.map((item: any, index: number) => ({
        id: item.id,
        rank: item.rank || ((filter.page || 1) - 1) * (filter.limit || 10) + index + 1,
        name: item.name,
        job: item.job,
        jobName: item.job_name || getJobName(item.job),
        level: item.level,
        exp: item.exp || 0,
        fame: item.fame || 0,
        quest_count: item.quest_count || 0,  // 修正字段名
        monster_book: item.monster_book || 0,  // 修正字段名
        avatar: item.avatar || `http://localhost:8080/api/avatar/${item.id}`,
        guild: item.guild ? {
          guildid: item.guild.guildid,
          name: item.guild.name,
          logo: item.guild.logo,
          logoColor: item.guild.logoColor,
          logoBG: item.guild.logoBG,
          logoBGColor: item.guild.logoBGColor,
          alliance_name: item.guild.alliance_name
        } : undefined,
        value: getValueBySort(item, filter.sort || 'level'),
        type: (filter.sort as any) || 'level'
      }));

      return {
        items: transformedItems,
        total: response.data.total,
        page: response.data.page || filter.page || 1,
        limit: response.data.size || filter.limit || 10,
        totalPages: Math.ceil(response.data.total / (response.data.size || filter.limit || 10))
      };
    } catch (error) {
      console.error('Failed to fetch character ranking:', error);
      throw error;
    }
  }

  /**
   * 获取公会排行榜
   */
  static async getGuildRanking(page = 1, limit = 10): Promise<GuildRankingResponse> {
    try {
      // 使用旧版API路径以保持兼容性
      const response = await request.get<{
        items: any[];
        total: number;
        page: number;
        size: number;
      }>(`/v1/game/guild/rank?page=${page}&size=${limit}`);

      // 转换数据格式以匹配新的类型定义
      const transformedItems: GuildRankingItem[] = response.data.items.map((item: any, index: number) => ({
        guildid: item.guildid,
        rank: (page - 1) * limit + index + 1,
        name: item.name,
        leader_name: item.leader_name || '未知',
        leader: item.leader || 0,
        member: item.member || 0,
        capacity: item.capacity || 0,
        GP: item.GP || 0,
        logo: item.logo,
        logoColor: item.logoColor,
        logoBG: item.logoBG,
        logoBGColor: item.logoBGColor,
        alliance_name: item.alliance_name,
        pub_notice: item.pub_notice,
        value: item.GP || 0
      }));

      return {
        items: transformedItems,
        total: response.data.total,
        page: response.data.page || page,
        limit: response.data.size || limit,
        totalPages: Math.ceil(response.data.total / (response.data.size || limit))
      };
    } catch (error) {
      console.error('Failed to fetch guild ranking:', error);
      throw error;
    }
  }

  /**
   * 生成邀请码
   */
  static async generateInviteCode(): Promise<{ code: string; expiresAt: string }> {
    const response = await request.post<{ code: string; expiresAt: string }>('/user/invite-code');
    return response.data;
  }

  /**
   * 获取角色详细信息
   */
  static async getCharacterDetail(characterId: number): Promise<Character & {
    stats: {
      str: number;
      dex: number;
      int: number;
      luk: number;
      hp: number;
      mp: number;
    };
    equipment: Array<{
      slot: string;
      itemId: number;
      itemName: string;
      icon?: string;
    }>;
    skills: Array<{
      id: number;
      name: string;
      level: number;
      maxLevel: number;
    }>;
  }> {
    const response = await request.get<Character & {
      stats: {
        str: number;
        dex: number;
        int: number;
        luk: number;
        hp: number;
        mp: number;
      };
      equipment: Array<{
        slot: string;
        itemId: number;
        itemName: string;
        icon?: string;
      }>;
      skills: Array<{
        id: number;
        name: string;
        level: number;
        maxLevel: number;
      }>;
    }>(`/character/${characterId}`);
    return response.data;
  }
}

// 辅助函数
function getJobName(jobId: number): string {
  if (jobId === 0) return '新手';
  if (jobId >= 100 && jobId < 200) return '战士';
  if (jobId >= 200 && jobId < 300) return '魔法师';
  if (jobId >= 300 && jobId < 400) return '弓箭手';
  if (jobId >= 400 && jobId < 500) return '飞侠';
  if (jobId >= 500 && jobId < 600) return '海盗';
  if (jobId >= 1000 && jobId < 1600) return '骑士团';
  if (jobId >= 2000) return '战神';
  return '未知';
}

function getValueBySort(item: any, sort: string): number {
  switch (sort) {
    case 'level':
      return item.level || 0;
    case 'fame':
      return item.fame || 0;
    case 'quest':
      return item.quest_count || 0;  // 修正字段名
    case 'monsterbook':
      return item.monster_book || 0;  // 修正字段名
    default:
      return item.level || 0;
  }
}

// 导出实例
export const gameService = GameService;
