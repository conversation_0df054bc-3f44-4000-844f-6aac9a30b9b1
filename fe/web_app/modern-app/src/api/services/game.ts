import { ApiService } from '../../lib/api';
import { 
  ServerStatus, 
  Character, 
  RankingItem, 
  RankingFilter, 
  PaginatedResponse 
} from '../../types';

export class GameService {
  /**
   * 获取服务器状态
   */
  static async getServerStatus(): Promise<ServerStatus> {
    const response = await ApiService.get<ServerStatus>('/game/status');
    return response.data;
  }

  /**
   * 获取角色列表
   */
  static async getCharacterList(): Promise<Character[]> {
    const response = await ApiService.get<Character[]>('/character/list');
    return response.data;
  }

  /**
   * 每日签到
   */
  static async checkIn(characterId: number): Promise<{ reward: string; nextReward?: string }> {
    const response = await ApiService.post<{ reward: string; nextReward?: string }>('/user/checkin', {
      character_id: characterId,
    });
    return response.data;
  }

  /**
   * 游戏解卡
   */
  static async unlockAccount(): Promise<{ message: string }> {
    const response = await ApiService.post<{ message: string }>('/game/ea');
    return response.data;
  }

  /**
   * 获取角色排行榜
   */
  static async getCharacterRanking(filter: RankingFilter = {}): Promise<PaginatedResponse<RankingItem>> {
    const params = new URLSearchParams();
    
    if (filter.job && filter.job !== 'all') params.append('job', filter.job);
    if (filter.sort) params.append('sort', filter.sort);
    if (filter.page) params.append('page', filter.page.toString());
    if (filter.limit) params.append('limit', filter.limit.toString());

    const response = await ApiService.get<PaginatedResponse<RankingItem>>(
      `/ranking/character?${params.toString()}`
    );
    return response.data;
  }

  /**
   * 获取公会排行榜
   */
  static async getGuildRanking(page = 1, limit = 50): Promise<PaginatedResponse<{
    rank: number;
    guildName: string;
    leaderName: string;
    memberCount: number;
    totalLevel: number;
    averageLevel: number;
  }>> {
    const response = await ApiService.get<PaginatedResponse<{
      rank: number;
      guildName: string;
      leaderName: string;
      memberCount: number;
      totalLevel: number;
      averageLevel: number;
    }>>(`/ranking/guild?page=${page}&limit=${limit}`);
    return response.data;
  }

  /**
   * 生成邀请码
   */
  static async generateInviteCode(): Promise<{ code: string; expiresAt: string }> {
    const response = await ApiService.post<{ code: string; expiresAt: string }>('/user/invite-code');
    return response.data;
  }

  /**
   * 获取角色详细信息
   */
  static async getCharacterDetail(characterId: number): Promise<Character & {
    stats: {
      str: number;
      dex: number;
      int: number;
      luk: number;
      hp: number;
      mp: number;
    };
    equipment: Array<{
      slot: string;
      itemId: number;
      itemName: string;
      icon?: string;
    }>;
    skills: Array<{
      id: number;
      name: string;
      level: number;
      maxLevel: number;
    }>;
  }> {
    const response = await ApiService.get<Character & {
      stats: {
        str: number;
        dex: number;
        int: number;
        luk: number;
        hp: number;
        mp: number;
      };
      equipment: Array<{
        slot: string;
        itemId: number;
        itemName: string;
        icon?: string;
      }>;
      skills: Array<{
        id: number;
        name: string;
        level: number;
        maxLevel: number;
      }>;
    }>(`/character/${characterId}`);
    return response.data;
  }
}
