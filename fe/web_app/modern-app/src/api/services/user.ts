import { request } from '../client';
import type {
  UserInfo,
  CharListResponse,
  CheckInRequest,
  CharItemResponse,
  CharEquipResponse
} from '../../types';

export class UserService {
  /**
   * 获取用户信息
   */
  static async getUserInfo(): Promise<UserInfo> {
    const data = await request.get<UserInfo>('/api/v1/user/info');
    return data;
  }

  /**
   * 每日签到
   */
  static async checkIn(characterId: number): Promise<{ message: string }> {
    const data = await request.post<{ message: string }>('/api/v1/user/checkin', {
      character_id: characterId,
    });
    return data;
  }

  /**
   * 申请邀请码
   */
  static async applyInviteCode(): Promise<{ message: string }> {
    const data = await request.post<{ message: string }>('/api/v1/user/apply_invite');
    return data;
  }

  /**
   * 获取角色列表
   */
  static async getCharacterList(): Promise<CharListResponse> {
    const data = await request.get<CharListResponse>('/api/v1/character/list');
    return data;
  }

  /**
   * 游戏解卡
   */
  static async unlockAccount(): Promise<{ message: string }> {
    const data = await request.post<{ message: string }>('/api/v1/game/ea');
    return data;
  }

  /**
   * 获取角色装备
   */
  static async getCharacterEquipment(characterId: number): Promise<CharEquipResponse> {
    const data = await request.get<CharEquipResponse>(`/api/v1/character/${characterId}/equip`);
    return data;
  }

  /**
   * 获取角色背包道具
   */
  static async getCharacterItems(characterId: number): Promise<CharItemResponse> {
    const data = await request.get<CharItemResponse>(`/api/v1/character/${characterId}/items`);
    return data;
  }
}
