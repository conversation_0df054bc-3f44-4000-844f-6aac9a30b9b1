import { request } from '../client';
import { Notice, PaginatedResponse } from '../../types';

export class NewsService {
  /**
   * 获取公告列表
   */
  static async getNoticeList(params: {
    size?: number;
    page?: number;
    category?: string;
  } = {}): Promise<PaginatedResponse<Notice>> {
    const { size = 30, page = 1, category } = params;
    const response = await request.get<{
      items: any[];
      total: number;
    }>('/api/v1/notice/list', {
      params: { size, page, category }
    });

    // 转换数据格式
    const transformedItems: Notice[] = response.items.map((item: any) => ({
      id: item.id,
      title: item.title,
      content: item.content || '',
      author: item.author || 'System',
      createdAt: item.create_time || item.createdAt,
      updatedAt: item.update_time || item.updatedAt,
      isTop: item.is_top || false,
      category: item.category || 'general',
      viewCount: item.view_count || 0,
    }));

    return {
      data: transformedItems,
      total: response.total,
      page: page,
      limit: size,
      totalPages: Math.ceil(response.total / size),
    };
  }

  /**
   * 获取公告详情
   */
  static async getNoticeDetail(id: number): Promise<Notice> {
    const response = await request.get<any>(`/api/v1/notice/${id}`);
    
    return {
      id: response.id,
      title: response.title,
      content: response.content || '',
      author: response.author || 'System',
      createdAt: response.create_time || response.createdAt,
      updatedAt: response.update_time || response.create_time,
      isTop: response.is_top || false,
      category: response.category || 'general',
      viewCount: response.view_count || 0,
    };
  }
}

// 导出实例
export const newsService = NewsService;
