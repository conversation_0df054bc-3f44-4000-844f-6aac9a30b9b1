import { request } from '../client';
import { Notice, PaginatedResponse } from '../../types';

export class NewsService {
  /**
   * 获取公告列表
   */
  static async getNoticeList(params: {
    size?: number;
    page?: number;
    category?: string;
  } = {}): Promise<PaginatedResponse<Notice>> {
    const { size = 30, page = 1, category } = params;
    const response = await request.get<{
      data: any[];
      total: number;
      page: number;
      size: number;
    }>('/v1/news/', {
      params: { size, page, category }
    });

    // 转换数据格式
    const transformedItems: Notice[] = response.data.data.map((item: any) => ({
      id: item.id,
      title: item.title,
      content: item.content || '',
      author: item.author || 'System',
      createdAt: item.create_time || item.createdAt,
      updatedAt: item.update_time || item.updatedAt,
      isTop: item.is_top || false,
      category: item.category || 'general',
      viewCount: item.view_count || 0,
    }));

    return {
      data: transformedItems,
      total: response.data.total,
      page: response.data.page,
      limit: response.data.size,
      totalPages: Math.ceil(response.data.total / response.data.size),
    };
  }

  /**
   * 获取公告详情
   */
  static async getNoticeDetail(id: number): Promise<Notice> {
    const response = await request.get<any>(`/v1/notice/${id}`);
    
    return {
      id: response.data.id,
      title: response.data.title,
      content: response.data.content || '',
      author: response.data.author || 'System',
      createdAt: response.data.create_time || response.data.createdAt,
      updatedAt: response.data.update_time || response.data.updatedAt,
      isTop: response.data.is_top || false,
      category: response.data.category || 'general',
      viewCount: response.data.view_count || 0,
    };
  }
}

// 导出实例
export const newsService = NewsService;
