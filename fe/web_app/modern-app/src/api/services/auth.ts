import { ApiService, TokenStorage } from '../../lib/api';
import { LoginRequest, LoginResponse, User } from '../../types';

export class AuthService {
  /**
   * 用户登录
   */
  static async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await ApiService.post<LoginResponse>('/auth/login', credentials);
    
    if (response.success && response.data) {
      // 保存token
      TokenStorage.setToken(response.data.token, response.data.expires_in);
      TokenStorage.setRefreshToken(response.data.refresh_token, response.data.refresh_expires_in);
    }
    
    return response.data;
  }

  /**
   * 用户登出
   */
  static async logout(): Promise<void> {
    try {
      await ApiService.post('/auth/logout');
    } finally {
      // 无论API调用是否成功，都清除本地token
      TokenStorage.clearAll();
    }
  }

  /**
   * 获取当前用户信息
   */
  static async getCurrentUser(): Promise<User> {
    const response = await ApiService.get<User>('/auth/me');
    return response.data;
  }

  /**
   * 刷新token
   */
  static async refreshToken(): Promise<{ token: string; expires_in: number }> {
    const refreshToken = TokenStorage.getRefreshToken();
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await ApiService.post<{ token: string; expires_in: number }>('/auth/refresh', {
      refresh_token: refreshToken,
    });

    if (response.success && response.data) {
      TokenStorage.setToken(response.data.token, response.data.expires_in);
    }

    return response.data;
  }

  /**
   * 检查是否已登录
   */
  static isAuthenticated(): boolean {
    return TokenStorage.getToken() !== null;
  }

  /**
   * 注册新用户
   */
  static async register(userData: {
    username: string;
    password: string;
    email?: string;
    inviteCode?: string;
  }): Promise<User> {
    const response = await ApiService.post<User>('/auth/register', userData);
    return response.data;
  }

  /**
   * 忘记密码
   */
  static async forgotPassword(email: string): Promise<void> {
    await ApiService.post('/auth/forgot-password', { email });
  }

  /**
   * 重置密码
   */
  static async resetPassword(token: string, newPassword: string): Promise<void> {
    await ApiService.post('/auth/reset-password', { token, password: newPassword });
  }
}
