import { request } from '../client';
import { LoginRequest, LoginResponse, User } from '../../types';

export class AuthService {
  /**
   * 用户登录
   */
  static async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await request.post<LoginResponse>('/auth/login', credentials);

    if (response.success && response.data) {
      // 保存token
      localStorage.setItem('access_token', response.data.token);
      localStorage.setItem('refresh_token', response.data.refresh_token);
    }

    return response.data;
  }

  /**
   * 用户登出
   */
  static async logout(): Promise<void> {
    try {
      await request.post('/auth/logout');
    } finally {
      // 无论API调用是否成功，都清除本地token
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
    }
  }

  /**
   * 获取当前用户信息
   */
  static async getCurrentUser(): Promise<User> {
    const response = await request.get<User>('/auth/me');
    return response.data;
  }

  /**
   * 刷新token
   */
  static async refreshToken(): Promise<{ token: string; expires_in: number }> {
    const refreshToken = localStorage.getItem('refresh_token');
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await request.post<{ token: string; expires_in: number }>('/auth/refresh', {
      refresh_token: refreshToken,
    });

    if (response.success && response.data) {
      localStorage.setItem('access_token', response.data.token);
    }

    return response.data;
  }

  /**
   * 检查是否已登录
   */
  static isAuthenticated(): boolean {
    return localStorage.getItem('access_token') !== null;
  }

  /**
   * 注册新用户
   */
  static async register(userData: {
    username: string;
    password: string;
    email?: string;
    inviteCode?: string;
  }): Promise<User> {
    const response = await request.post<User>('/auth/register', userData);
    return response.data;
  }

  /**
   * 忘记密码
   */
  static async forgotPassword(email: string): Promise<void> {
    await request.post('/auth/forgot-password', { email });
  }

  /**
   * 重置密码
   */
  static async resetPassword(token: string, newPassword: string): Promise<void> {
    await request.post('/auth/reset-password', { token, password: newPassword });
  }
}

// 导出实例
export const authService = AuthService;
