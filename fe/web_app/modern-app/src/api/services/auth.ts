import { request } from '../client';
import { LoginRequest, LoginResponse, RegisterRequest, User, TermsOfService } from '../../types';

export class AuthService {
  /**
   * 用户登录
   */
  static async login(credentials: LoginRequest): Promise<LoginResponse> {
    // 根据OpenAPI规范，使用正确的端点和请求格式
    const data = await request.post<LoginResponse>('/api/v1/auth/login', {
      username: credentials.username,
      password: credentials.password
    });

    if (data) {
      // 根据OpenAPI规范保存token和过期时间
      const nowTime = Date.now();
      const tokenExpireTime = data.expires_in * 1000 + nowTime;
      const refreshExpireTime = data.refresh_expires_in * 1000 + nowTime;

      localStorage.setItem('access_token', data.token);
      localStorage.setItem('refresh_token', data.refresh_token);
      localStorage.setItem('token_expire_time', tokenExpireTime.toString());
      localStorage.setItem('refresh_token_expire_time', refreshExpireTime.toString());
    }

    return data;
  }

  /**
   * 用户登出
   */
  static async logout(): Promise<void> {
    try {
      // 注意：OpenAPI中没有logout端点，所以只清除本地token
      // await request.post('/api/v1/auth/logout');
    } finally {
      // 清除所有本地token
      this.clearTokens();
    }
  }

  /**
   * 获取当前用户信息
   */
  static async getCurrentUser(): Promise<User> {
    const data = await request.get<User>('/api/v1/user/info');
    return data;
  }

  /**
   * 刷新token
   */
  static async refreshToken(): Promise<{ token: string; expires_in: number }> {
    const refreshToken = localStorage.getItem('refresh_token');
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    // 根据OpenAPI规范使用正确的端点
    const data = await request.post<{ token: string; refresh_token: string; expires_in: number; refresh_expires_in: number }>('/api/v1/auth/refresh', {}, {
      headers: {
        'Authorization': `Bearer ${refreshToken}`
      }
    });

    if (data) {
      const nowTime = Date.now();
      const tokenExpireTime = data.expires_in * 1000 + nowTime;
      const refreshExpireTime = data.refresh_expires_in * 1000 + nowTime;

      localStorage.setItem('access_token', data.token);
      localStorage.setItem('refresh_token', data.refresh_token);
      localStorage.setItem('token_expire_time', tokenExpireTime.toString());
      localStorage.setItem('refresh_token_expire_time', refreshExpireTime.toString());
    }

    return data;
  }

  /**
   * 检查是否已登录
   */
  static isAuthenticated(): boolean {
    const token = localStorage.getItem('access_token');
    const expireTime = localStorage.getItem('token_expire_time');

    if (!token || !expireTime) {
      return false;
    }

    // 检查token是否过期
    const now = Date.now();
    const expire = parseInt(expireTime);

    if (now >= expire) {
      // Token已过期，尝试自动刷新
      this.tryRefreshToken();
      return false;
    }

    return true;
  }

  /**
   * 尝试自动刷新token
   */
  private static async tryRefreshToken(): Promise<void> {
    try {
      const refreshToken = localStorage.getItem('refresh_token');
      const refreshExpireTime = localStorage.getItem('refresh_token_expire_time');

      if (!refreshToken || !refreshExpireTime) {
        this.clearTokens();
        return;
      }

      const now = Date.now();
      const refreshExpire = parseInt(refreshExpireTime);

      if (now >= refreshExpire) {
        // Refresh token也过期了
        this.clearTokens();
        return;
      }

      // 尝试刷新token
      await this.refreshToken();
    } catch (error) {
      console.error('Auto refresh token failed:', error);
      this.clearTokens();
    }
  }

  /**
   * 清除所有token
   */
  private static clearTokens(): void {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('token_expire_time');
    localStorage.removeItem('refresh_token_expire_time');
  }

  /**
   * 发送注册邮箱验证码
   */
  static async sendRegisterEmailCode(email: string): Promise<any> {
    const data = await request.post<any>('/register/captcha', { email });
    return data;
  }

  /**
   * 获取服务条款内容
   */
  static async getTermsOfService(): Promise<TermsOfService> {
    const data = await request.get<TermsOfService>('/api/tos');
    return data;
  }

  /**
   * 注册新用户
   */
  static async register(userData: RegisterRequest): Promise<any> {
    // 根据OpenAPI规范构建请求体
    const requestBody = {
      username: userData.username,
      email: userData.email,
      code: userData.emailCode,
      pwd1: userData.password,
      pwd2: userData.confirmPassword,
      birthday: userData.birthday,
      invitation_code: userData.invitationCode || '',
      captcha: userData.captcha || ''
    };

    const data = await request.post<any>('/api/v1/auth/register', requestBody);
    return data;
  }

  /**
   * 忘记密码
   */
  static async forgotPassword(email: string): Promise<void> {
    await request.post('/auth/forgot-password', { email });
  }

  /**
   * 重置密码
   */
  static async resetPassword(token: string, newPassword: string): Promise<void> {
    await request.post('/auth/reset-password', { token, password: newPassword });
  }
}

// 导出实例
export const authService = AuthService;
