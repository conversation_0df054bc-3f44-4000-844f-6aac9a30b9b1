import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/Card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '../../components/ui/Tabs';
import { Alert, AlertDescription } from '../../components/ui/Alert';
import { RankingIconFilters } from '../../components/ranking/RankingIconFilters';
import { CharacterRankingTable } from '../../components/ranking/CharacterRankingTable';
import { GuildRankingTable } from '../../components/ranking/GuildRankingTable';
import { RankingPagination } from '../../components/ranking/RankingPagination';
import { gameService } from '../../api/services/game';
import { RankingFilter, RankingResponse, GuildRankingResponse } from '../../types';
import { AlertCircle } from 'lucide-react';

const RankingPage = () => {
  const { t } = useTranslation('common');

  // 状态管理
  const [activeTab, setActiveTab] = useState<'character' | 'guild'>('character');
  const [characterData, setCharacterData] = useState<RankingResponse | null>(null);
  const [guildData, setGuildData] = useState<GuildRankingResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 筛选条件
  const [characterFilter, setCharacterFilter] = useState<RankingFilter>({
    job: 'all',
    sort: 'level',
    page: 1,
    limit: 10
  });

  const [guildPage, setGuildPage] = useState(1);
  const guildLimit = 10;

  // 获取角色排行榜数据
  const fetchCharacterRanking = useCallback(async (filter: RankingFilter) => {
    try {
      setLoading(true);
      setError(null);
      const response = await gameService.getCharacterRanking(filter);
      setCharacterData(response);
    } catch (err) {
      console.error('Failed to fetch character ranking:', err);
      setError(t('ranking.error'));
    } finally {
      setLoading(false);
    }
  }, [t]);

  // 获取公会排行榜数据
  const fetchGuildRanking = useCallback(async (page: number) => {
    try {
      setLoading(true);
      setError(null);
      const response = await gameService.getGuildRanking(page, guildLimit);
      setGuildData(response);
    } catch (err) {
      console.error('Failed to fetch guild ranking:', err);
      setError(t('ranking.error'));
    } finally {
      setLoading(false);
    }
  }, [t]);

  // 初始化数据加载
  useEffect(() => {
    if (activeTab === 'character') {
      fetchCharacterRanking(characterFilter);
    } else {
      fetchGuildRanking(guildPage);
    }
  }, [activeTab, characterFilter, guildPage, fetchCharacterRanking, fetchGuildRanking]);

  // 处理角色排行榜筛选变化
  const handleCharacterFilterChange = (newFilter: RankingFilter) => {
    setCharacterFilter(newFilter);
  };

  // 处理角色排行榜分页
  const handleCharacterPageChange = (page: number) => {
    setCharacterFilter(prev => ({ ...prev, page }));
  };

  // 处理公会排行榜分页
  const handleGuildPageChange = (page: number) => {
    setGuildPage(page);
  };

  // 刷新数据
  const handleRefresh = () => {
    if (activeTab === 'character') {
      fetchCharacterRanking(characterFilter);
    } else {
      fetchGuildRanking(guildPage);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Card>
        <CardHeader>
          <CardTitle>{t('ranking.title')}</CardTitle>
          <CardDescription>{t('ranking.description')}</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'character' | 'guild')}>
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="character">角色排行</TabsTrigger>
              <TabsTrigger value="guild">公会排行</TabsTrigger>
            </TabsList>

            {/* 错误提示 */}
            {error && (
              <Alert variant="destructive" className="mb-6">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* 角色排行榜 */}
            <TabsContent value="character" className="space-y-6">
              <RankingIconFilters
                filter={characterFilter}
                onFilterChange={handleCharacterFilterChange}
                disabled={loading}
              />

              {characterData && (
                <>
                  <CharacterRankingTable
                    data={characterData.items}
                    sortType={characterFilter.sort as 'level' | 'fame' | 'quest' | 'monsterbook'}
                    loading={loading}
                  />

                  <RankingPagination
                    currentPage={characterData.page}
                    totalPages={characterData.totalPages}
                    totalItems={characterData.total}
                    itemsPerPage={characterData.limit}
                    onPageChange={handleCharacterPageChange}
                    onRefresh={handleRefresh}
                    loading={loading}
                  />
                </>
              )}

              {loading && !characterData && (
                <div className="text-center py-8">
                  <div className="text-gray-500">{t('ranking.loading')}</div>
                </div>
              )}
            </TabsContent>

            {/* 公会排行榜 */}
            <TabsContent value="guild" className="space-y-6">
              {guildData && (
                <>
                  <GuildRankingTable
                    data={guildData.items}
                    loading={loading}
                  />

                  <RankingPagination
                    currentPage={guildData.page}
                    totalPages={guildData.totalPages}
                    totalItems={guildData.total}
                    itemsPerPage={guildData.limit}
                    onPageChange={handleGuildPageChange}
                    onRefresh={handleRefresh}
                    loading={loading}
                  />
                </>
              )}

              {loading && !guildData && (
                <div className="text-center py-8">
                  <div className="text-gray-500">{t('ranking.loading')}</div>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default RankingPage;
