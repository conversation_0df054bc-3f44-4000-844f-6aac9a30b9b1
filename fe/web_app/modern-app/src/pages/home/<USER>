import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Link } from 'react-router-dom';
import { ROUTES } from '../../lib/router';
import { Download, Users, Trophy, ShoppingBag } from 'lucide-react';

const HomePage = () => {
  const { t } = useTranslation();

  const features = [
    {
      icon: <Download className="h-8 w-8" />,
      title: "快速下载",
      description: "一键下载游戏客户端，快速开始游戏",
      link: ROUTES.DOWNLOAD,
    },
    {
      icon: <Trophy className="h-8 w-8" />,
      title: "排行榜",
      description: "查看角色和公会排行榜，展示你的实力",
      link: ROUTES.RANKING,
    },
    {
      icon: <ShoppingBag className="h-8 w-8" />,
      title: "商城",
      description: "购买游戏道具，提升游戏体验",
      link: ROUTES.SHOP,
    },
    {
      icon: <Users className="h-8 w-8" />,
      title: "社区",
      description: "加入我们的社区，与其他玩家交流",
      link: ROUTES.NOTICE,
    },
  ];

  const serverRates = [
    { name: "经验倍率", value: "5x", color: "text-green-600" },
    { name: "金币倍率", value: "3x", color: "text-yellow-600" },
    { name: "掉落倍率", value: "2x", color: "text-blue-600" },
    { name: "BOSS倍率", value: "2x", color: "text-red-600" },
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Hero Section */}
      <section className="text-center py-16">
        <h1 className="text-4xl md:text-6xl font-bold mb-6">
          {t('home.welcome')}
        </h1>
        <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
          {t('home.description')}
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button size="lg" asChild>
            <Link to={ROUTES.DOWNLOAD}>
              <Download className="mr-2 h-4 w-4" />
              立即下载
            </Link>
          </Button>
          <Button variant="outline" size="lg" asChild>
            <Link to={ROUTES.AUTH.REGISTER}>
              <Users className="mr-2 h-4 w-4" />
              注册账号
            </Link>
          </Button>
        </div>
      </section>

      {/* Server Info */}
      <section className="py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">{t('home.serverInfo.title')}</h2>
          <p className="text-muted-foreground">实时服务器状态和倍率信息</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {serverRates.map((rate, index) => (
            <Card key={index} className="text-center">
              <CardHeader>
                <CardTitle className={rate.color}>{rate.value}</CardTitle>
                <CardDescription>{rate.name}</CardDescription>
              </CardHeader>
            </Card>
          ))}
        </div>

        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              服务器在线
            </CardTitle>
            <CardDescription>
              当前在线人数: <span className="font-semibold text-primary">1,234</span> 人
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <p className="text-muted-foreground">服务器时间</p>
                <p className="font-semibold">{new Date().toLocaleString('zh-CN')}</p>
              </div>
              <div>
                <p className="text-muted-foreground">版本</p>
                <p className="font-semibold">v83</p>
              </div>
              <div>
                <p className="text-muted-foreground">最高等级</p>
                <p className="font-semibold">255</p>
              </div>
              <div>
                <p className="text-muted-foreground">运行时间</p>
                <p className="font-semibold">99.9%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Features */}
      <section className="py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">{t('home.features.title')}</h2>
          <p className="text-muted-foreground">探索MagicMS的精彩功能</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => (
            <Card key={index} className="group hover:shadow-lg transition-shadow cursor-pointer">
              <Link to={feature.link}>
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 p-3 rounded-full bg-primary/10 text-primary group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                    {feature.icon}
                  </div>
                  <CardTitle>{feature.title}</CardTitle>
                  <CardDescription>{feature.description}</CardDescription>
                </CardHeader>
              </Link>
            </Card>
          ))}
        </div>
      </section>

      {/* Game Features */}
      <section className="py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">游戏特色</h2>
          <p className="text-muted-foreground">独特的游戏机制和自定义功能</p>
        </div>
        
        <Card>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="font-semibold mb-4">核心特色</h3>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>• 全局怪物点券祝福混沌掉落</li>
                  <li>• 加入装备等级系统</li>
                  <li>• 卷轴效果多倍加成，混沌卷轴不减属性</li>
                  <li>• 暗黑狂潮事件，怪物双倍血量三倍数量</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold mb-4">职业增强</h3>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>• 支持同一职业下的四转分支职业自由转换</li>
                  <li>• 魔法师专属buff汉斯的祝福</li>
                  <li>• 弓箭手专属buff赫丽娜的祝福</li>
                  <li>• 战神专属buff利琳的信仰</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>
    </div>
  );
};

export default HomePage;
