import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/Card';
import { Button } from '../../../components/ui/Button';
import { Alert, AlertDescription } from '../../../components/ui/Alert';
import { LoadingSpinner } from '../../../components/common';
import { UserService } from '../../../api/services/user';
import { 
  Shield, 
  Mail, 
  CheckCircle, 
  AlertCircle,
  Unlock,
  UserPlus
} from 'lucide-react';

const QuickActionsCard: React.FC = () => {
  const { t } = useTranslation('profile');
  const [loading, setLoading] = useState<{ [key: string]: boolean }>({});
  const [messages, setMessages] = useState<{ [key: string]: { type: 'success' | 'error'; text: string } }>({});

  // 设置加载状态
  const setActionLoading = (action: string, isLoading: boolean) => {
    setLoading(prev => ({ ...prev, [action]: isLoading }));
  };

  // 设置消息
  const setActionMessage = (action: string, type: 'success' | 'error', text: string) => {
    setMessages(prev => ({ ...prev, [action]: { type, text } }));
    // 3秒后清除消息
    setTimeout(() => {
      setMessages(prev => {
        const newMessages = { ...prev };
        delete newMessages[action];
        return newMessages;
      });
    }, 3000);
  };

  // 处理卡号自救
  const handleUnlockAccount = async () => {
    try {
      setActionLoading('unlock', true);
      setActionMessage('unlock', 'success', ''); // 清除之前的消息
      
      const result = await UserService.unlockAccount();
      setActionMessage('unlock', 'success', result.message || t('quickActions.unlock.success'));
    } catch (error: any) {
      console.error('Account unlock failed:', error);
      const errorMessage = error.response?.data?.message || error.message || t('quickActions.unlock.error');
      setActionMessage('unlock', 'error', errorMessage);
    } finally {
      setActionLoading('unlock', false);
    }
  };

  // 处理申请邀请码
  const handleApplyInviteCode = async () => {
    try {
      setActionLoading('invite', true);
      setActionMessage('invite', 'success', ''); // 清除之前的消息
      
      const result = await UserService.applyInviteCode();
      setActionMessage('invite', 'success', result.message || t('quickActions.invite.success'));
    } catch (error: any) {
      console.error('Apply invite code failed:', error);
      const errorMessage = error.response?.data?.message || error.message || t('quickActions.invite.error');
      setActionMessage('invite', 'error', errorMessage);
    } finally {
      setActionLoading('invite', false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          {t('quickActions.title')}
        </CardTitle>
        <CardDescription>{t('quickActions.description')}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 卡号自救 */}
        <div className="space-y-2">
          <Button 
            onClick={handleUnlockAccount}
            disabled={loading.unlock}
            variant="outline"
            className="w-full justify-start"
          >
            {loading.unlock ? (
              <LoadingSpinner size="sm" className="mr-2" />
            ) : (
              <Unlock className="h-4 w-4 mr-2" />
            )}
            {t('quickActions.unlock.title')}
          </Button>
          
          {messages.unlock && (
            <Alert variant={messages.unlock.type === 'error' ? 'destructive' : 'default'}>
              {messages.unlock.type === 'error' ? (
                <AlertCircle className="h-4 w-4" />
              ) : (
                <CheckCircle className="h-4 w-4" />
              )}
              <AlertDescription>{messages.unlock.text}</AlertDescription>
            </Alert>
          )}
          
          <p className="text-xs text-muted-foreground">
            {t('quickActions.unlock.description')}
          </p>
        </div>

        <div className="border-t pt-4">
          {/* 申请邀请码 */}
          <div className="space-y-2">
            <Button 
              onClick={handleApplyInviteCode}
              disabled={loading.invite}
              variant="outline"
              className="w-full justify-start"
            >
              {loading.invite ? (
                <LoadingSpinner size="sm" className="mr-2" />
              ) : (
                <UserPlus className="h-4 w-4 mr-2" />
              )}
              {t('quickActions.invite.title')}
            </Button>
            
            {messages.invite && (
              <Alert variant={messages.invite.type === 'error' ? 'destructive' : 'default'}>
                {messages.invite.type === 'error' ? (
                  <AlertCircle className="h-4 w-4" />
                ) : (
                  <CheckCircle className="h-4 w-4" />
                )}
                <AlertDescription>{messages.invite.text}</AlertDescription>
              </Alert>
            )}
            
            <p className="text-xs text-muted-foreground">
              {t('quickActions.invite.description')}
            </p>
          </div>
        </div>

        {/* 帮助信息 */}
        <div className="border-t pt-4">
          <div className="text-sm text-muted-foreground bg-muted p-3 rounded-lg">
            <div className="flex items-start gap-2">
              <Mail className="h-4 w-4 mt-0.5 text-blue-500" />
              <div>
                <div className="font-medium mb-1">{t('quickActions.help.title')}</div>
                <p className="text-xs">{t('quickActions.help.description')}</p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default QuickActionsCard;
