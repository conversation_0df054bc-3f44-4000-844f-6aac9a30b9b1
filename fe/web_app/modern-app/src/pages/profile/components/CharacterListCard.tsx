import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/Card';
import { Badge } from '../../../components/ui/Badge';
import { Button } from '../../../components/ui/Button';
import type { CharInfo } from '../../../types';
import { 
  Users, 
  Star, 
  Coins, 
  Trophy,
  Calendar,
  MapPin,
  Eye,
  Package
} from 'lucide-react';

interface CharacterListCardProps {
  characters: CharInfo[];
  onViewEquipment?: (character: CharInfo) => void;
  onViewInventory?: (character: CharInfo) => void;
}

const CharacterListCard: React.FC<CharacterListCardProps> = ({
  characters,
  onViewEquipment,
  onViewInventory
}) => {
  const { t } = useTranslation('profile');

  // 获取职业名称
  const getJobName = (jobId: number) => {
    const jobMap: { [key: number]: string } = {
      0: t('jobs.beginner'),
      100: t('jobs.warrior'),
      200: t('jobs.magician'),
      300: t('jobs.bowman'),
      400: t('jobs.thief'),
      500: t('jobs.pirate'),
      1000: t('jobs.noblesse'),
      2000: t('jobs.aran'),
      2001: t('jobs.evan'),
    };
    
    // 根据职业ID范围判断
    if (jobId >= 100 && jobId < 200) return t('jobs.warrior');
    if (jobId >= 200 && jobId < 300) return t('jobs.magician');
    if (jobId >= 300 && jobId < 400) return t('jobs.bowman');
    if (jobId >= 400 && jobId < 500) return t('jobs.thief');
    if (jobId >= 500 && jobId < 600) return t('jobs.pirate');
    if (jobId >= 1000 && jobId < 1100) return t('jobs.noblesse');
    if (jobId >= 2000 && jobId < 2100) return t('jobs.aran');
    if (jobId >= 2100 && jobId < 2200) return t('jobs.evan');
    
    return jobMap[jobId] || t('jobs.unknown');
  };

  // 获取职业颜色
  const getJobColor = (jobId: number) => {
    if (jobId >= 100 && jobId < 200) return 'bg-red-100 text-red-800';
    if (jobId >= 200 && jobId < 300) return 'bg-blue-100 text-blue-800';
    if (jobId >= 300 && jobId < 400) return 'bg-green-100 text-green-800';
    if (jobId >= 400 && jobId < 500) return 'bg-purple-100 text-purple-800';
    if (jobId >= 500 && jobId < 600) return 'bg-orange-100 text-orange-800';
    if (jobId >= 1000 && jobId < 1100) return 'bg-yellow-100 text-yellow-800';
    if (jobId >= 2000 && jobId < 2100) return 'bg-pink-100 text-pink-800';
    if (jobId >= 2100 && jobId < 2200) return 'bg-indigo-100 text-indigo-800';
    return 'bg-gray-100 text-gray-800';
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // 处理查看装备
  const handleViewEquipment = (characterId: number) => {
    const character = characters.find(c => c.id === characterId);
    if (character && onViewEquipment) {
      onViewEquipment(character);
    }
  };

  // 处理查看背包
  const handleViewInventory = (characterId: number) => {
    const character = characters.find(c => c.id === characterId);
    if (character && onViewInventory) {
      onViewInventory(character);
    }
  };

  if (characters.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            {t('characters.title')}
          </CardTitle>
          <CardDescription>{t('characters.description')}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>{t('characters.empty')}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          {t('characters.title')}
          <Badge variant="secondary">{characters.length}</Badge>
        </CardTitle>
        <CardDescription>{t('characters.description')}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {characters.map((character) => (
          <div key={character.id} className="border rounded-lg p-4 space-y-3">
            {/* 角色基本信息 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold">
                  {character.name.charAt(0).toUpperCase()}
                </div>
                <div>
                  <h4 className="font-semibold">{character.name}</h4>
                  <div className="flex items-center gap-2">
                    <Badge className={getJobColor(character.job)}>
                      {getJobName(character.job)}
                    </Badge>
                    <span className="text-sm text-muted-foreground">Lv.{character.level}</span>
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <Trophy className="h-3 w-3" />
                  <span>#{character.rank}</span>
                </div>
              </div>
            </div>

            {/* 角色属性 */}
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="flex items-center gap-1">
                <Star className="h-3 w-3 text-yellow-500" />
                <span className="text-muted-foreground">{t('characters.fame')}:</span>
                <span>{character.fame}</span>
              </div>
              <div className="flex items-center gap-1">
                <Coins className="h-3 w-3 text-yellow-600" />
                <span className="text-muted-foreground">{t('characters.meso')}:</span>
                <span>{character.meso.toLocaleString()}</span>
              </div>
            </div>

            {/* 角色状态 */}
            <div className="text-xs text-muted-foreground space-y-1">
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                <span>{t('characters.created')}: {formatDate(character.create_date)}</span>
              </div>
              <div className="flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                <span>{t('characters.lastLogout')}: {formatDate(character.last_logout_time)}</span>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex gap-2 pt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleViewEquipment(character.id)}
                className="flex-1"
              >
                <Eye className="h-3 w-3 mr-1" />
                {t('characters.viewEquipment')}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleViewInventory(character.id)}
                className="flex-1"
              >
                <Package className="h-3 w-3 mr-1" />
                {t('characters.viewInventory')}
              </Button>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};

export default CharacterListCard;
