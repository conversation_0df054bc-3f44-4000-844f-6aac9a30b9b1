import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../../../components/ui/Dialog';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/Card';
import { Button } from '../../../components/ui/Button';
import { Badge } from '../../../components/ui/Badge';
import { Alert, AlertDescription } from '../../../components/ui/Alert';
import { LoadingSpinner } from '../../../components/common';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../components/ui/Tabs';
import { UserService } from '../../../api/services/user';
import type { InvItemModel, CharInfo } from '../../../types';
import {
  Package,
  Flask,
  Gem,
  Scroll,
  Coins,
  Gift,
  AlertCircle,
  RefreshCw,
  Hash,
  Calendar,
  User
} from 'lucide-react';

interface InventoryModalProps {
  character: CharInfo | null;
  isOpen: boolean;
  onClose: () => void;
}

// 背包类型映射
const INVENTORY_TYPES = {
  1: { name: '装备', icon: Package, color: 'bg-blue-100 text-blue-800' },
  2: { name: '消耗品', icon: Flask, color: 'bg-green-100 text-green-800' },
  3: { name: '设置', icon: Scroll, color: 'bg-purple-100 text-purple-800' },
  4: { name: '其他', icon: Gem, color: 'bg-yellow-100 text-yellow-800' },
  5: { name: '现金', icon: Coins, color: 'bg-orange-100 text-orange-800' },
};

export const InventoryModal: React.FC<InventoryModalProps> = ({
  character,
  isOpen,
  onClose,
}) => {
  const { t } = useTranslation('profile');
  const [items, setItems] = useState<InvItemModel[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('all');

  // 加载背包数据
  const loadInventory = async () => {
    if (!character) return;

    try {
      setLoading(true);
      setError(null);
      const data = await UserService.getCharacterItems(character.id);
      setItems(data.items || []);
    } catch (err: any) {
      console.error('Failed to load inventory:', err);
      setError(err.response?.data?.message || err.message || t('inventory.errors.loadFailed'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen && character) {
      loadInventory();
    }
  }, [isOpen, character]);

  // 重试加载
  const handleRetry = () => {
    loadInventory();
  };

  // 获取背包类型信息
  const getInventoryTypeInfo = (type: number) => {
    return INVENTORY_TYPES[type as keyof typeof INVENTORY_TYPES] || {
      name: `类型${type}`,
      icon: Package,
      color: 'bg-gray-100 text-gray-800'
    };
  };

  // 按类型分组道具
  const groupItemsByType = () => {
    const grouped: { [key: number]: InvItemModel[] } = {};
    items.forEach(item => {
      if (!grouped[item.inventory_type]) {
        grouped[item.inventory_type] = [];
      }
      grouped[item.inventory_type].push(item);
    });
    return grouped;
  };

  // 过滤道具
  const getFilteredItems = () => {
    if (activeTab === 'all') return items;
    const typeId = parseInt(activeTab);
    return items.filter(item => item.inventory_type === typeId);
  };

  // 渲染道具卡片
  const renderItemCard = (item: InvItemModel) => {
    const typeInfo = getInventoryTypeInfo(item.inventory_type);
    const IconComponent = typeInfo.icon;

    return (
      <Card key={item.id} className="hover:shadow-md transition-shadow">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <IconComponent className="h-5 w-5 text-muted-foreground" />
              <CardTitle className="text-sm font-medium">
                {t('inventory.itemId', { id: item.item_id })}
              </CardTitle>
            </div>
            <Badge variant="outline" className={typeInfo.color}>
              {typeInfo.name}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-3">
            {/* 基础信息 */}
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">{t('inventory.position')}:</span>
                <span className="font-mono">{item.position}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">{t('inventory.quantity')}:</span>
                <span className="font-medium">{item.quantity}</span>
              </div>
            </div>

            {/* 道具类型 */}
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">{t('inventory.type')}:</span>
              <span>{item.type}</span>
            </div>

            {/* 所有者信息 */}
            {item.owner && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">{t('inventory.owner')}:</span>
                <div className="flex items-center space-x-1">
                  <User className="h-3 w-3" />
                  <span>{item.owner}</span>
                </div>
              </div>
            )}

            {/* 宠物ID */}
            {item.pet_id > 0 && (
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">{t('inventory.petId')}:</span>
                <span className="font-mono">{item.pet_id}</span>
              </div>
            )}

            {/* 标志 */}
            {item.flag > 0 && (
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">{t('inventory.flag')}:</span>
                <span className="font-mono">{item.flag}</span>
              </div>
            )}

            {/* 过期时间 */}
            {item.expiration > 0 && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">{t('inventory.expiration')}:</span>
                <div className="flex items-center space-x-1">
                  <Calendar className="h-3 w-3 text-orange-500" />
                  <span className="text-orange-600">
                    {new Date(item.expiration * 1000).toLocaleDateString()}
                  </span>
                </div>
              </div>
            )}

            {/* 礼物来源 */}
            {item.gift_from && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">{t('inventory.giftFrom')}:</span>
                <div className="flex items-center space-x-1">
                  <Gift className="h-3 w-3 text-pink-500" />
                  <span className="text-pink-600">{item.gift_from}</span>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  // 获取统计信息
  const getStats = () => {
    const grouped = groupItemsByType();
    const stats = Object.entries(grouped).map(([typeId, typeItems]) => {
      const typeInfo = getInventoryTypeInfo(parseInt(typeId));
      return {
        type: typeInfo.name,
        count: typeItems.length,
        totalQuantity: typeItems.reduce((sum, item) => sum + item.quantity, 0),
        color: typeInfo.color
      };
    });
    return stats;
  };

  const filteredItems = getFilteredItems();
  const stats = getStats();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Package className="h-5 w-5" />
            <span>
              {character ? 
                t('inventory.title', { characterName: character.name }) : 
                t('inventory.title', { characterName: '' })
              }
            </span>
          </DialogTitle>
          <DialogDescription>
            {t('inventory.description')}
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <LoadingSpinner size="lg" />
            </div>
          ) : error ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>{error}</span>
                <Button onClick={handleRetry} variant="outline" size="sm">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  {t('common.retry')}
                </Button>
              </AlertDescription>
            </Alert>
          ) : items.length === 0 ? (
            <div className="text-center py-12">
              <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">{t('inventory.noItems')}</p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* 统计信息 */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {stats.map((stat, index) => (
                  <Card key={index} className="text-center">
                    <CardContent className="pt-4">
                      <Badge className={stat.color}>{stat.type}</Badge>
                      <div className="mt-2 text-2xl font-bold">{stat.count}</div>
                      <div className="text-xs text-muted-foreground">
                        {t('inventory.totalQuantity')}: {stat.totalQuantity}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* 分类标签 */}
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-6">
                  <TabsTrigger value="all">{t('inventory.tabs.all')}</TabsTrigger>
                  {Object.entries(INVENTORY_TYPES).map(([typeId, typeInfo]) => (
                    <TabsTrigger key={typeId} value={typeId}>
                      {typeInfo.name}
                    </TabsTrigger>
                  ))}
                </TabsList>

                <TabsContent value={activeTab} className="mt-4">
                  <div className="max-h-96 overflow-y-auto">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {filteredItems.map(renderItemCard)}
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
