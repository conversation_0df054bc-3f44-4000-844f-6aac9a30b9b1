import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/Card';
import { Button } from '../../../components/ui/Button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../components/ui/Select';
import { Alert, AlertDescription } from '../../../components/ui/Alert';
import { LoadingSpinner } from '../../../components/common';
import { UserService } from '../../../api/services/user';
import type { CharInfo } from '../../../types';
import { 
  Calendar, 
  Gift, 
  CheckCircle, 
  AlertCircle,
  Star
} from 'lucide-react';

interface CheckInCardProps {
  characters: CharInfo[];
  onCheckInSuccess?: () => void;
}

const CheckInCard: React.FC<CheckInCardProps> = ({ characters, onCheckInSuccess }) => {
  const { t } = useTranslation('profile');
  const [selectedCharacterId, setSelectedCharacterId] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  // 获取职业名称
  const getJobName = (jobId: number) => {
    const jobMap: { [key: number]: string } = {
      0: t('jobs.beginner'),
      100: t('jobs.warrior'),
      200: t('jobs.magician'),
      300: t('jobs.bowman'),
      400: t('jobs.thief'),
      500: t('jobs.pirate'),
      1000: t('jobs.noblesse'),
      2000: t('jobs.aran'),
      2001: t('jobs.evan'),
    };
    
    // 根据职业ID范围判断
    if (jobId >= 100 && jobId < 200) return t('jobs.warrior');
    if (jobId >= 200 && jobId < 300) return t('jobs.magician');
    if (jobId >= 300 && jobId < 400) return t('jobs.bowman');
    if (jobId >= 400 && jobId < 500) return t('jobs.thief');
    if (jobId >= 500 && jobId < 600) return t('jobs.pirate');
    if (jobId >= 1000 && jobId < 1100) return t('jobs.noblesse');
    if (jobId >= 2000 && jobId < 2100) return t('jobs.aran');
    if (jobId >= 2100 && jobId < 2200) return t('jobs.evan');
    
    return jobMap[jobId] || t('jobs.unknown');
  };

  // 处理签到
  const handleCheckIn = async () => {
    if (!selectedCharacterId) {
      setMessage({ type: 'error', text: t('checkIn.errors.noCharacterSelected') });
      return;
    }

    try {
      setLoading(true);
      setMessage(null);
      
      const result = await UserService.checkIn(parseInt(selectedCharacterId));
      setMessage({ type: 'success', text: result.message || t('checkIn.success') });
      
      // 签到成功后刷新用户信息
      if (onCheckInSuccess) {
        onCheckInSuccess();
      }
      
      // 清空选择
      setSelectedCharacterId('');
    } catch (error: any) {
      console.error('Check-in failed:', error);
      const errorMessage = error.response?.data?.message || error.message || t('checkIn.errors.failed');
      setMessage({ type: 'error', text: errorMessage });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          {t('checkIn.title')}
        </CardTitle>
        <CardDescription>{t('checkIn.description')}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 角色选择 */}
        <div className="space-y-2">
          <label className="text-sm font-medium">{t('checkIn.selectCharacter')}</label>
          <Select value={selectedCharacterId} onValueChange={setSelectedCharacterId}>
            <SelectTrigger>
              <SelectValue placeholder={t('checkIn.selectCharacterPlaceholder')} />
            </SelectTrigger>
            <SelectContent>
              {characters.map((character) => (
                <SelectItem key={character.id} value={character.id.toString()}>
                  <div className="flex items-center gap-2">
                    <Star className="h-4 w-4 text-yellow-500" />
                    <span className="font-medium">{character.name}</span>
                    <span className="text-muted-foreground">
                      Lv.{character.level} {getJobName(character.job)}
                    </span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* 签到按钮 */}
        <Button 
          onClick={handleCheckIn} 
          disabled={!selectedCharacterId || loading}
          className="w-full"
          size="lg"
        >
          {loading ? (
            <>
              <LoadingSpinner size="sm" className="mr-2" />
              {t('checkIn.processing')}
            </>
          ) : (
            <>
              <Gift className="h-4 w-4 mr-2" />
              {t('checkIn.button')}
            </>
          )}
        </Button>

        {/* 消息显示 */}
        {message && (
          <Alert variant={message.type === 'error' ? 'destructive' : 'default'}>
            {message.type === 'error' ? (
              <AlertCircle className="h-4 w-4" />
            ) : (
              <CheckCircle className="h-4 w-4" />
            )}
            <AlertDescription>{message.text}</AlertDescription>
          </Alert>
        )}

        {/* 签到说明 */}
        <div className="text-sm text-muted-foreground bg-muted p-3 rounded-lg">
          <div className="flex items-start gap-2">
            <Gift className="h-4 w-4 mt-0.5 text-blue-500" />
            <div>
              <div className="font-medium mb-1">{t('checkIn.info.title')}</div>
              <ul className="space-y-1 text-xs">
                <li>• {t('checkIn.info.daily')}</li>
                <li>• {t('checkIn.info.selectCharacter')}</li>
                <li>• {t('checkIn.info.rewards')}</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default CheckInCard;
