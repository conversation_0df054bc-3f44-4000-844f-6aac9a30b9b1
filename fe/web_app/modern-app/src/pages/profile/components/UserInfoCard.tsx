import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/Card';
import { Badge } from '../../../components/ui/Badge';
import { Button } from '../../../components/ui/Button';
import type { UserInfo } from '../../../types';
import {
  User,
  Mail,
  Calendar,
  Clock,
  Coins,
  Gift,
  Shield,
  Crown,
  Loader2,
  AlertCircle,
  RefreshCw
} from 'lucide-react';

interface UserInfoCardProps {
  userInfo: UserInfo | null;
  loading: boolean;
  error: string | null;
  onRetry: () => void;
}

const UserInfoCard: React.FC<UserInfoCardProps> = ({ userInfo, loading, error, onRetry }) => {
  const { t } = useTranslation('profile');

  // 加载状态
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            {t('userInfo.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  // 错误状态或无数据
  if (error || !userInfo) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            {t('userInfo.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
            <p className="text-muted-foreground mb-4">
              {error || t('userInfo.noData')}
            </p>
            <Button onClick={onRetry} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              {t('common.retry')}
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // 格式化日期
  const formatDate = (dateString: string | null) => {
    if (!dateString) return t('common.never');
    return new Date(dateString).toLocaleString();
  };

  // 获取性别显示
  const getGenderDisplay = (gender: number) => {
    return gender === 0 ? t('userInfo.gender.male') : t('userInfo.gender.female');
  };

  // 获取账号状态
  const getAccountStatus = () => {
    if (userInfo.banned > 0) {
      return (
        <Badge variant="destructive">
          <Shield className="h-3 w-3 mr-1" />
          {t('userInfo.status.banned')}
        </Badge>
      );
    }
    return (
      <Badge variant="default">
        <Shield className="h-3 w-3 mr-1" />
        {t('userInfo.status.normal')}
      </Badge>
    );
  };

  // 获取管理员状态
  const getAdminStatus = () => {
    if (userInfo.web_admin && userInfo.web_admin > 0) {
      return (
        <Badge variant="secondary">
          <Crown className="h-3 w-3 mr-1" />
          {t('userInfo.status.admin')}
        </Badge>
      );
    }
    return null;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          {t('userInfo.title')}
        </CardTitle>
        <CardDescription>{t('userInfo.description')}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 基本信息 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">{t('userInfo.username')}:</span>
              <span className="text-sm">{userInfo.name}</span>
            </div>
            
            {userInfo.nick && (
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">{t('userInfo.nickname')}:</span>
                <span className="text-sm">{userInfo.nick}</span>
              </div>
            )}

            {userInfo.email && (
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">{t('userInfo.email')}:</span>
                <span className="text-sm">{userInfo.email}</span>
              </div>
            )}

            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">{t('userInfo.registerDate')}:</span>
              <span className="text-sm">{formatDate(userInfo.create_at)}</span>
            </div>

            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">{t('userInfo.lastLogin')}:</span>
              <span className="text-sm">{userInfo.last_login ? formatDate(userInfo.last_login) : 'NA'}</span>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">{t('userInfo.gender')}:</span>
              <span className="text-sm">{getGenderDisplay(userInfo.gender)}</span>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">{t('userInfo.characterSlots')}:</span>
              <span className="text-sm">{userInfo.slots}</span>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">{t('userInfo.status.title')}:</span>
              <div className="flex gap-2">
                {getAccountStatus()}
                {getAdminStatus()}
              </div>
            </div>
          </div>
        </div>

        {/* 游戏货币信息 */}
        <div className="border-t pt-4">
          <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
            <Coins className="h-4 w-4" />
            {t('userInfo.currency.title')}
          </h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-muted rounded-lg">
              <div className="text-lg font-bold text-orange-600">{userInfo.nx.toLocaleString()}</div>
              <div className="text-xs text-muted-foreground">{t('userInfo.currency.nxCredit')}</div>
            </div>
            <div className="text-center p-3 bg-muted rounded-lg">
              <div className="text-lg font-bold text-blue-600">{userInfo.mp.toLocaleString()}</div>
              <div className="text-xs text-muted-foreground">{t('userInfo.currency.maplePoint')}</div>
            </div>
            <div className="text-center p-3 bg-muted rounded-lg">
              <div className="text-lg font-bold text-green-600">{userInfo.np.toLocaleString()}</div>
              <div className="text-xs text-muted-foreground">{t('userInfo.currency.nxPrepaid')}</div>
            </div>
            <div className="text-center p-3 bg-muted rounded-lg">
              <div className="text-lg font-bold text-purple-600">{userInfo.reward_point.toLocaleString()}</div>
              <div className="text-xs text-muted-foreground">{t('userInfo.currency.rewardPoints')}</div>
            </div>
          </div>
        </div>

        {/* 投票积分 */}
        <div className="border-t pt-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Gift className="h-4 w-4" />
              <span className="text-sm font-medium">{t('userInfo.votePoints')}:</span>
            </div>
            <Badge variant="outline" className="text-lg px-3 py-1">
              {userInfo.vote_points.toLocaleString()}
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default UserInfoCard;
