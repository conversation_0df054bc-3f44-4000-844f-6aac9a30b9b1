import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';
import { Badge } from '../../components/ui/Badge';
import { Alert, AlertDescription } from '../../components/ui/Alert';
import { LoadingSpinner } from '../../components/common';
import { UserService } from '../../api/services/user';
import type { UserInfo, CharInfo } from '../../types';
import {
  User,
  Calendar,
  Clock,
  Coins,
  Gift,
  Shield,
  Users,
  AlertCircle,
  CheckCircle,
  RefreshCw
} from 'lucide-react';

// 子组件
import {
  UserInfoCard,
  CheckInCard,
  QuickActionsCard,
  CharacterListCard
} from './components';

const ProfilePage = () => {
  const { t } = useTranslation('profile');
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [characters, setCharacters] = useState<CharInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 加载用户信息
  const loadUserInfo = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await UserService.getUserInfo();
      setUserInfo(data);
    } catch (err) {
      console.error('Failed to load user info:', err);
      setError(t('errors.loadUserInfo'));
    } finally {
      setLoading(false);
    }
  };

  // 加载角色列表
  const loadCharacters = async () => {
    try {
      const data = await UserService.getCharacterList();
      setCharacters(data.items || []);
    } catch (err) {
      console.error('Failed to load characters:', err);
      // 角色列表加载失败不阻塞主要功能
    }
  };

  useEffect(() => {
    loadUserInfo();
    loadCharacters();
  }, []);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button
              variant="outline"
              size="sm"
              className="ml-4"
              onClick={loadUserInfo}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              {t('actions.retry')}
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">{t('title')}</h1>
        <p className="text-muted-foreground">{t('description')}</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧列 - 用户信息和签到 */}
        <div className="lg:col-span-2 space-y-6">
          <UserInfoCard
            userInfo={userInfo}
            loading={userLoading}
            error={userError}
            onRetry={loadUserInfo}
          />
          <CheckInCard characters={characters} onCheckInSuccess={loadUserInfo} />
        </div>

        {/* 右侧列 - 快速操作和角色列表 */}
        <div className="space-y-6">
          <QuickActionsCard />
          <CharacterListCard characters={characters} />
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
