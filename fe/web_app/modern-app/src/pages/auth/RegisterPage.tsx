import { useState, useCallback } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Eye, EyeOff, Mail, Calendar, User, Lock, Shield } from 'lucide-react';
import toast from 'react-hot-toast';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Button,
  Input,
  Form,
  FormField,
  FormLabel,
  FormControl,
  FormMessage,
  Checkbox
} from '../../components/ui';
import { LoadingSpinner } from '../../components/common';
import { ROUTES } from '../../lib/router';
import { AuthService } from '../../api/services/auth';
import TermsOfServiceModal from '../../components/auth/TermsOfServiceModal';

interface RegisterFormData {
  username: string;
  email: string;
  emailCode: string;
  password: string;
  confirmPassword: string;
  birthday: string;
  inviteCode: string;
  agreeTerms: boolean;
}

interface FormErrors {
  [key: string]: string;
}

const RegisterPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [sendingCode, setSendingCode] = useState(false);
  const [codeCountdown, setCodeCountdown] = useState(0);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const [formData, setFormData] = useState<RegisterFormData>({
    username: '',
    email: '',
    emailCode: '',
    password: '',
    confirmPassword: '',
    birthday: '',
    inviteCode: '',
    agreeTerms: false
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [showTermsModal, setShowTermsModal] = useState(false);
  const [hasAcceptedTerms, setHasAcceptedTerms] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // 清除对应字段的错误
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // 用户名验证
    if (!formData.username) {
      newErrors.username = t('auth.register.validation.usernameRequired');
    } else if (formData.username.length < 5 || formData.username.length > 12) {
      newErrors.username = t('auth.register.validation.usernameLength');
    } else if (!/^[0-9a-zA-Z]{5,12}$/.test(formData.username)) {
      newErrors.username = t('auth.register.validation.usernameFormat');
    }

    // 邮箱验证
    if (!formData.email) {
      newErrors.email = t('auth.register.validation.emailRequired');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = t('auth.register.validation.emailFormat');
    }

    // 邮箱验证码验证
    if (!formData.emailCode) {
      newErrors.emailCode = t('auth.register.validation.emailCodeRequired');
    }

    // 密码验证
    if (!formData.password) {
      newErrors.password = t('auth.register.validation.passwordRequired');
    } else if (formData.password.length < 6 || formData.password.length > 16) {
      newErrors.password = t('auth.register.validation.passwordLength');
    } else if (!/^[0-9a-zA-Z~!@#$%^&*()_\-+=<>?:"{}|,./;'\\[\]·！￥…（）—《》？：""【】、；'，。]{6,16}$/.test(formData.password)) {
      newErrors.password = t('auth.register.validation.passwordFormat');
    }

    // 确认密码验证
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = t('auth.register.validation.confirmPasswordRequired');
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = t('auth.register.validation.passwordMismatch');
    }

    // 生日验证
    if (!formData.birthday) {
      newErrors.birthday = t('auth.register.validation.birthdayRequired');
    }

    // 服务条款验证
    if (!formData.agreeTerms) {
      newErrors.agreeTerms = t('auth.register.validation.termsRequired');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 发送邮箱验证码
  const handleSendEmailCode = useCallback(async () => {
    if (!formData.email) {
      setErrors(prev => ({ ...prev, email: t('auth.register.validation.emailRequired') }));
      return;
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      setErrors(prev => ({ ...prev, email: t('auth.register.validation.emailFormat') }));
      return;
    }

    try {
      setSendingCode(true);
      // 调用发送邮箱验证码API
      await AuthService.sendRegisterEmailCode(formData.email);

      toast.success(t('auth.register.emailCodeSent'));

      // 开始倒计时
      setCodeCountdown(60);
      const timer = setInterval(() => {
        setCodeCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

    } catch (error: any) {
      toast.error(error.response?.data?.message || t('auth.register.emailCodeError'));
    } finally {
      setSendingCode(false);
    }
  }, [formData.email, t]);

  // 处理服务条款接受
  const handleTermsAccept = () => {
    setHasAcceptedTerms(true);
    setFormData(prev => ({ ...prev, agreeTerms: true }));
    // 清除服务条款相关的错误
    if (errors.agreeTerms) {
      setErrors(prev => ({ ...prev, agreeTerms: undefined }));
    }
  };

  // 提交注册
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);

      await AuthService.register({
        username: formData.username,
        email: formData.email,
        password: formData.password,
        confirmPassword: formData.confirmPassword,
        emailCode: formData.emailCode,
        birthday: formData.birthday,
        invitationCode: formData.inviteCode || '',
        captcha: formData.captcha || ''
      });

      toast.success(t('auth.register.success'));
      navigate(ROUTES.AUTH.LOGIN);
    } catch (error: any) {
      toast.error(error.response?.data?.message || t('auth.register.error'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-lg mx-auto">
        <Card>
          <CardHeader className="text-center">
            <CardTitle className="text-2xl">{t('auth.register.title')}</CardTitle>
            <CardDescription>{t('auth.register.description')}</CardDescription>
          </CardHeader>
          <CardContent>
            <Form onSubmit={handleSubmit}>
              {/* 用户名 */}
              <FormField>
                <FormLabel htmlFor="username">
                  <User className="h-4 w-4 inline mr-2" />
                  {t('auth.register.username')}
                </FormLabel>
                <FormControl>
                  <Input
                    id="username"
                    name="username"
                    type="text"
                    placeholder={t('auth.register.usernamePlaceholder')}
                    value={formData.username}
                    onChange={handleInputChange}
                    disabled={loading}
                    maxLength={12}
                  />
                </FormControl>
                {errors.username && <FormMessage>{errors.username}</FormMessage>}
              </FormField>

              {/* 邮箱 */}
              <FormField>
                <FormLabel htmlFor="email">
                  <Mail className="h-4 w-4 inline mr-2" />
                  {t('auth.register.email')}
                </FormLabel>
                <FormControl>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder={t('auth.register.emailPlaceholder')}
                    value={formData.email}
                    onChange={handleInputChange}
                    disabled={loading}
                  />
                </FormControl>
                {errors.email && <FormMessage>{errors.email}</FormMessage>}
              </FormField>

              {/* 邮箱验证码 */}
              <FormField>
                <FormLabel htmlFor="emailCode">
                  <Shield className="h-4 w-4 inline mr-2" />
                  {t('auth.register.emailCode')}
                </FormLabel>
                <FormControl>
                  <div className="flex gap-2">
                    <Input
                      id="emailCode"
                      name="emailCode"
                      type="text"
                      placeholder={t('auth.register.emailCodePlaceholder')}
                      value={formData.emailCode}
                      onChange={handleInputChange}
                      disabled={loading}
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleSendEmailCode}
                      disabled={sendingCode || codeCountdown > 0 || loading}
                      className="whitespace-nowrap"
                    >
                      {sendingCode ? (
                        <>
                          <LoadingSpinner size="sm" className="mr-2" />
                          {t('auth.register.sending')}
                        </>
                      ) : codeCountdown > 0 ? (
                        `${codeCountdown}s`
                      ) : (
                        t('auth.register.sendCode')
                      )}
                    </Button>
                  </div>
                </FormControl>
                {errors.emailCode && <FormMessage>{errors.emailCode}</FormMessage>}
              </FormField>

              {/* 密码 */}
              <FormField>
                <FormLabel htmlFor="password">
                  <Lock className="h-4 w-4 inline mr-2" />
                  {t('auth.register.password')}
                </FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      id="password"
                      name="password"
                      type={showPassword ? "text" : "password"}
                      placeholder={t('auth.register.passwordPlaceholder')}
                      value={formData.password}
                      onChange={handleInputChange}
                      disabled={loading}
                      maxLength={16}
                    />
                    <button
                      type="button"
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                </FormControl>
                {errors.password && <FormMessage>{errors.password}</FormMessage>}
              </FormField>

              {/* 确认密码 */}
              <FormField>
                <FormLabel htmlFor="confirmPassword">
                  <Lock className="h-4 w-4 inline mr-2" />
                  {t('auth.register.confirmPassword')}
                </FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      id="confirmPassword"
                      name="confirmPassword"
                      type={showConfirmPassword ? "text" : "password"}
                      placeholder={t('auth.register.confirmPasswordPlaceholder')}
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      disabled={loading}
                      maxLength={16}
                    />
                    <button
                      type="button"
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    >
                      {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                </FormControl>
                {errors.confirmPassword && <FormMessage>{errors.confirmPassword}</FormMessage>}
              </FormField>

              {/* 生日 */}
              <FormField>
                <FormLabel htmlFor="birthday">
                  <Calendar className="h-4 w-4 inline mr-2" />
                  {t('auth.register.birthday')}
                </FormLabel>
                <FormControl>
                  <Input
                    id="birthday"
                    name="birthday"
                    type="date"
                    value={formData.birthday}
                    onChange={handleInputChange}
                    disabled={loading}
                  />
                </FormControl>
                {errors.birthday && <FormMessage>{errors.birthday}</FormMessage>}
              </FormField>

              {/* 邀请码（可选） */}
              <FormField>
                <FormLabel htmlFor="inviteCode">
                  {t('auth.register.inviteCode')}
                </FormLabel>
                <FormControl>
                  <Input
                    id="inviteCode"
                    name="inviteCode"
                    type="text"
                    placeholder={t('auth.register.inviteCodePlaceholder')}
                    value={formData.inviteCode}
                    onChange={handleInputChange}
                    disabled={loading}
                  />
                </FormControl>
              </FormField>

              {/* 服务条款同意 */}
              <FormField>
                <div className="space-y-3">
                  {/* 阅读服务条款按钮 */}
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full"
                    onClick={() => setShowTermsModal(true)}
                  >
                    <Shield className="w-4 h-4 mr-2" />
                    {t('auth.register.readTerms')}
                  </Button>

                  {/* 确认已阅读的复选框 */}
                  {hasAcceptedTerms && (
                    <div className="flex items-start space-x-2">
                      <Checkbox
                        id="agreeTerms"
                        name="agreeTerms"
                        checked={formData.agreeTerms}
                        onCheckedChange={(checked) =>
                          setFormData(prev => ({ ...prev, agreeTerms: checked as boolean }))
                        }
                      />
                      <FormLabel htmlFor="agreeTerms" className="text-sm font-normal leading-5 text-green-600">
                        {t('auth.register.terms')}
                      </FormLabel>
                    </div>
                  )}

                  {!hasAcceptedTerms && (
                    <p className="text-sm text-muted-foreground">
                      {t('auth.register.mustScrollToBottom')}
                    </p>
                  )}
                </div>
                {errors.agreeTerms && <FormMessage>{errors.agreeTerms}</FormMessage>}
              </FormField>

              {/* 提交按钮 */}
              <Button type="submit" className="w-full" disabled={loading}>
                {loading ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    {t('auth.register.submitting')}
                  </>
                ) : (
                  t('auth.register.submit')
                )}
              </Button>
            </Form>

            {/* 登录链接 */}
            <div className="mt-6 text-center">
              <p className="text-sm text-muted-foreground">
                已有账号？{' '}
                <Link to={ROUTES.AUTH.LOGIN} className="text-primary hover:underline">
                  立即登录
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 服务条款模态框 */}
      <TermsOfServiceModal
        open={showTermsModal}
        onOpenChange={setShowTermsModal}
        onAccept={handleTermsAccept}
      />
    </div>
  );
};

export default RegisterPage;
