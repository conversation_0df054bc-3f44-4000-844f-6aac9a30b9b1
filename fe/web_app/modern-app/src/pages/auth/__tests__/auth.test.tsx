import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../../lib/i18n';
import LoginPage from '../LoginPage';
import RegisterPage from '../RegisterPage';

// Mock AuthService
jest.mock('../../../api/services/auth', () => ({
  AuthService: {
    login: jest.fn(),
    register: jest.fn(),
  },
}));

// Mock react-hot-toast
jest.mock('react-hot-toast', () => ({
  default: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      <I18nextProvider i18n={i18n}>
        {component}
      </I18nextProvider>
    </BrowserRouter>
  );
};

describe('Authentication Pages', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('LoginPage', () => {
    it('renders login form correctly', () => {
      renderWithProviders(<LoginPage />);
      
      expect(screen.getByText('登录')).toBeInTheDocument();
      expect(screen.getByLabelText('用户名')).toBeInTheDocument();
      expect(screen.getByLabelText('密码')).toBeInTheDocument();
      expect(screen.getByText('记住我')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: '登录' })).toBeInTheDocument();
    });

    it('validates required fields', async () => {
      renderWithProviders(<LoginPage />);
      
      const submitButton = screen.getByRole('button', { name: '登录' });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('请输入用户名')).toBeInTheDocument();
        expect(screen.getByText('请输入密码')).toBeInTheDocument();
      });
    });

    it('toggles password visibility', () => {
      renderWithProviders(<LoginPage />);
      
      const passwordInput = screen.getByLabelText('密码') as HTMLInputElement;
      const toggleButton = screen.getByRole('button', { name: '' }); // Eye icon button
      
      expect(passwordInput.type).toBe('password');
      
      fireEvent.click(toggleButton);
      expect(passwordInput.type).toBe('text');
      
      fireEvent.click(toggleButton);
      expect(passwordInput.type).toBe('password');
    });
  });

  describe('RegisterPage', () => {
    it('renders register form correctly', () => {
      renderWithProviders(<RegisterPage />);
      
      expect(screen.getByText('注册')).toBeInTheDocument();
      expect(screen.getByLabelText('用户名')).toBeInTheDocument();
      expect(screen.getByLabelText('邮箱')).toBeInTheDocument();
      expect(screen.getByLabelText('邮箱验证码')).toBeInTheDocument();
      expect(screen.getByLabelText('密码')).toBeInTheDocument();
      expect(screen.getByLabelText('确认密码')).toBeInTheDocument();
      expect(screen.getByLabelText('生日')).toBeInTheDocument();
      expect(screen.getByLabelText('邀请码（可选）')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: '注册' })).toBeInTheDocument();
    });

    it('validates username format', async () => {
      renderWithProviders(<RegisterPage />);
      
      const usernameInput = screen.getByLabelText('用户名');
      const submitButton = screen.getByRole('button', { name: '注册' });
      
      // Test short username
      fireEvent.change(usernameInput, { target: { value: 'abc' } });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText('用户名长度必须为5-12位')).toBeInTheDocument();
      });
      
      // Test invalid characters
      fireEvent.change(usernameInput, { target: { value: 'user@123' } });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText('用户名只能包含字母和数字')).toBeInTheDocument();
      });
    });

    it('validates password confirmation', async () => {
      renderWithProviders(<RegisterPage />);
      
      const passwordInput = screen.getByLabelText('密码');
      const confirmPasswordInput = screen.getByLabelText('确认密码');
      const submitButton = screen.getByRole('button', { name: '注册' });
      
      fireEvent.change(passwordInput, { target: { value: 'password123' } });
      fireEvent.change(confirmPasswordInput, { target: { value: 'password456' } });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText('两次输入的密码不一致')).toBeInTheDocument();
      });
    });

    it('validates email format', async () => {
      renderWithProviders(<RegisterPage />);
      
      const emailInput = screen.getByLabelText('邮箱');
      const submitButton = screen.getByRole('button', { name: '注册' });
      
      fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText('请输入有效的邮箱地址')).toBeInTheDocument();
      });
    });

    it('requires terms agreement', async () => {
      renderWithProviders(<RegisterPage />);
      
      const submitButton = screen.getByRole('button', { name: '注册' });
      fireEvent.click(submitButton);
      
      await waitFor(() => {
        expect(screen.getByText('请同意服务条款和隐私政策')).toBeInTheDocument();
      });
    });
  });
});
