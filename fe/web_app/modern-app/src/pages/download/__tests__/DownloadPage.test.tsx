import { render, screen } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { I18nextProvider } from 'react-i18next';
import i18n from '../../../i18n';
import DownloadPage from '../DownloadPage';

// Mock组件以避免复杂的依赖
jest.mock('../../../components/download/ClientPreview', () => ({
  ClientPreview: () => <div data-testid="client-preview">Client Preview</div>
}));

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      <I18nextProvider i18n={i18n}>
        {component}
      </I18nextProvider>
    </BrowserRouter>
  );
};

describe('DownloadPage', () => {
  beforeEach(() => {
    // 确保i18n已初始化
    i18n.changeLanguage('zh');
  });

  test('renders download page title', async () => {
    renderWithProviders(<DownloadPage />);
    
    // 等待翻译加载
    await screen.findByText('游戏下载');
    expect(screen.getByText('游戏下载')).toBeInTheDocument();
  });

  test('renders client preview component', () => {
    renderWithProviders(<DownloadPage />);
    
    expect(screen.getByTestId('client-preview')).toBeInTheDocument();
  });

  test('renders download options', async () => {
    renderWithProviders(<DownloadPage />);
    
    // 检查下载选项标题
    await screen.findByText('下载选项');
    expect(screen.getByText('下载选项')).toBeInTheDocument();
  });

  test('renders system requirements', async () => {
    renderWithProviders(<DownloadPage />);
    
    // 检查系统要求标题
    await screen.findByText('系统要求');
    expect(screen.getByText('系统要求')).toBeInTheDocument();
  });

  test('renders installation guide', async () => {
    renderWithProviders(<DownloadPage />);
    
    // 检查安装指南标题
    await screen.findByText('安装指南');
    expect(screen.getByText('安装指南')).toBeInTheDocument();
  });

  test('renders troubleshooting section', async () => {
    renderWithProviders(<DownloadPage />);
    
    // 检查故障排除标题
    await screen.findByText('常见问题解决方案');
    expect(screen.getByText('常见问题解决方案')).toBeInTheDocument();
  });

  test('renders support section', async () => {
    renderWithProviders(<DownloadPage />);
    
    // 检查技术支持标题
    await screen.findByText('技术支持');
    expect(screen.getByText('技术支持')).toBeInTheDocument();
  });

  test('renders important notices', async () => {
    renderWithProviders(<DownloadPage />);
    
    // 检查邀请码提醒
    await screen.findByText(/MagicMS已启用邀请码注册/);
    expect(screen.getByText(/MagicMS已启用邀请码注册/)).toBeInTheDocument();
  });
});
