import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Button,
  Input,
  Badge,
  Alert,
  AlertDescription,
  AlertTitle,
  Avatar,
  AvatarFallback,
  AvatarImage,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Checkbox,
  Textarea,
  Form,
  FormField,
  FormLabel,
  FormControl
} from '../../components/ui';
import { LoadingSpinner } from '../../components/common';
import { ServerStatus, CharacterCard, RankingTable } from '../../components/game';
import { 
  AlertCircle, 
  CheckCircle, 
  Info, 
  AlertTriangle,
  Star,
  Heart,
  Download
} from 'lucide-react';

const ComponentsDemo = () => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectValue, setSelectValue] = useState('');
  const [checkboxChecked, setCheckboxChecked] = useState(false);

  // 模拟角色数据
  const mockCharacter = {
    id: 1,
    name: '测试角色',
    job: '战士',
    level: 120,
    exp: 1234567,
    meso: 9876543,
    str: 150,
    dex: 80,
    int: 50,
    luk: 60,
    guildName: '测试公会',
    lastLogin: new Date().toISOString(),
    createTime: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    avatar: null
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-4xl font-bold mb-4">组件展示</h1>
        <p className="text-muted-foreground">MagicMS 现代化组件库展示页面</p>
      </div>

      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="basic">基础组件</TabsTrigger>
          <TabsTrigger value="forms">表单组件</TabsTrigger>
          <TabsTrigger value="data">数据展示</TabsTrigger>
          <TabsTrigger value="game">游戏组件</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="mt-6 space-y-6">
          {/* Buttons */}
          <Card>
            <CardHeader>
              <CardTitle>按钮组件</CardTitle>
              <CardDescription>不同样式和大小的按钮</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-2">
                <Button>默认按钮</Button>
                <Button variant="secondary">次要按钮</Button>
                <Button variant="destructive">危险按钮</Button>
                <Button variant="outline">边框按钮</Button>
                <Button variant="ghost">幽灵按钮</Button>
                <Button variant="link">链接按钮</Button>
              </div>
              <div className="flex flex-wrap gap-2">
                <Button size="sm">小按钮</Button>
                <Button size="default">默认按钮</Button>
                <Button size="lg">大按钮</Button>
                <Button size="icon">
                  <Star className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Badges */}
          <Card>
            <CardHeader>
              <CardTitle>徽章组件</CardTitle>
              <CardDescription>用于状态标识的徽章</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                <Badge>默认</Badge>
                <Badge variant="secondary">次要</Badge>
                <Badge variant="destructive">危险</Badge>
                <Badge variant="outline">边框</Badge>
                <Badge variant="success">成功</Badge>
                <Badge variant="warning">警告</Badge>
                <Badge variant="info">信息</Badge>
              </div>
            </CardContent>
          </Card>

          {/* Alerts */}
          <Card>
            <CardHeader>
              <CardTitle>警告组件</CardTitle>
              <CardDescription>不同类型的提示信息</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>信息提示</AlertTitle>
                <AlertDescription>这是一个普通的信息提示。</AlertDescription>
              </Alert>
              <Alert variant="success">
                <CheckCircle className="h-4 w-4" />
                <AlertTitle>成功</AlertTitle>
                <AlertDescription>操作已成功完成！</AlertDescription>
              </Alert>
              <Alert variant="warning">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>警告</AlertTitle>
                <AlertDescription>请注意这个重要信息。</AlertDescription>
              </Alert>
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>错误</AlertTitle>
                <AlertDescription>发生了一个错误，请重试。</AlertDescription>
              </Alert>
            </CardContent>
          </Card>

          {/* Avatars */}
          <Card>
            <CardHeader>
              <CardTitle>头像组件</CardTitle>
              <CardDescription>用户头像展示</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-4">
                <Avatar>
                  <AvatarImage src="https://github.com/shadcn.png" alt="@shadcn" />
                  <AvatarFallback>CN</AvatarFallback>
                </Avatar>
                <Avatar>
                  <AvatarFallback>JD</AvatarFallback>
                </Avatar>
                <Avatar className="h-16 w-16">
                  <AvatarFallback>LG</AvatarFallback>
                </Avatar>
              </div>
            </CardContent>
          </Card>

          {/* Loading */}
          <Card>
            <CardHeader>
              <CardTitle>加载组件</CardTitle>
              <CardDescription>加载状态指示器</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-4">
                <LoadingSpinner size="sm" />
                <LoadingSpinner size="default" />
                <LoadingSpinner size="lg" />
                <LoadingSpinner text="加载中..." />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="forms" className="mt-6 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>表单组件</CardTitle>
              <CardDescription>各种表单输入组件</CardDescription>
            </CardHeader>
            <CardContent>
              <Form className="space-y-4">
                <FormField>
                  <FormLabel>输入框</FormLabel>
                  <FormControl>
                    <Input placeholder="请输入内容..." />
                  </FormControl>
                </FormField>

                <FormField>
                  <FormLabel>文本域</FormLabel>
                  <FormControl>
                    <Textarea placeholder="请输入多行文本..." />
                  </FormControl>
                </FormField>

                <FormField>
                  <FormLabel>选择器</FormLabel>
                  <FormControl>
                    <Select value={selectValue} onValueChange={setSelectValue}>
                      <SelectTrigger>
                        <SelectValue placeholder="请选择..." />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="option1">选项 1</SelectItem>
                        <SelectItem value="option2">选项 2</SelectItem>
                        <SelectItem value="option3">选项 3</SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                </FormField>

                <FormField>
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="demo-checkbox"
                      checked={checkboxChecked}
                      onCheckedChange={setCheckboxChecked}
                    />
                    <FormLabel htmlFor="demo-checkbox">复选框选项</FormLabel>
                  </div>
                </FormField>

                <Button type="submit">提交表单</Button>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="data" className="mt-6 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>表格组件</CardTitle>
              <CardDescription>数据表格展示</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>姓名</TableHead>
                    <TableHead>职业</TableHead>
                    <TableHead>等级</TableHead>
                    <TableHead>状态</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell>张三</TableCell>
                    <TableCell>战士</TableCell>
                    <TableCell>120</TableCell>
                    <TableCell><Badge variant="success">在线</Badge></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>李四</TableCell>
                    <TableCell>法师</TableCell>
                    <TableCell>95</TableCell>
                    <TableCell><Badge variant="secondary">离线</Badge></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>王五</TableCell>
                    <TableCell>弓箭手</TableCell>
                    <TableCell>88</TableCell>
                    <TableCell><Badge variant="success">在线</Badge></TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>对话框组件</CardTitle>
              <CardDescription>模态对话框</CardDescription>
            </CardHeader>
            <CardContent>
              <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
                <DialogTrigger asChild>
                  <Button>打开对话框</Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>对话框标题</DialogTitle>
                    <DialogDescription>
                      这是一个示例对话框，用于展示对话框组件的功能。
                    </DialogDescription>
                  </DialogHeader>
                  <div className="py-4">
                    <p>对话框内容区域...</p>
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setDialogOpen(false)}>
                      取消
                    </Button>
                    <Button onClick={() => setDialogOpen(false)}>
                      确认
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="game" className="mt-6 space-y-6">
          <ServerStatus />
          
          <Card>
            <CardHeader>
              <CardTitle>角色卡片</CardTitle>
              <CardDescription>游戏角色信息展示</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="max-w-md">
                <CharacterCard character={mockCharacter} />
              </div>
            </CardContent>
          </Card>

          <RankingTable />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ComponentsDemo;
