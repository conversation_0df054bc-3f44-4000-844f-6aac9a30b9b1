import { gameService } from '../api/services/game';
import { ServerStatus } from '../types';

const SERVER_STATUS_KEY = 'ServerStatus';
const FORCE_INVITE_KEY = 'forceInvite';
const CACHE_DURATION = 60000; // 60秒缓存

interface CachedServerStatus {
  data: ServerStatus;
  timestamp: number;
}

type StatusListener = (status: ServerStatus | null, loading: boolean, error: string | null) => void;

/**
 * 全局服务器状态管理器 - 单例模式
 * 确保整个应用只有一个实例在管理服务器状态，避免重复请求
 */
class ServerStatusManager {
  private static instance: ServerStatusManager;
  private status: ServerStatus | null = null;
  private loading = false;
  private error: string | null = null;
  private listeners: Set<StatusListener> = new Set();
  private fetchPromise: Promise<ServerStatus> | null = null;
  private refreshTimer: NodeJS.Timeout | null = null;

  private constructor() {
    // 私有构造函数，确保单例
  }

  static getInstance(): ServerStatusManager {
    if (!ServerStatusManager.instance) {
      ServerStatusManager.instance = new ServerStatusManager();
    }
    return ServerStatusManager.instance;
  }

  // 添加状态监听器
  addListener(listener: StatusListener): () => void {
    this.listeners.add(listener);
    // 立即通知当前状态
    listener(this.status, this.loading, this.error);
    
    // 返回取消监听的函数
    return () => {
      this.listeners.delete(listener);
    };
  }

  // 通知所有监听器
  private notifyListeners() {
    this.listeners.forEach(listener => {
      listener(this.status, this.loading, this.error);
    });
  }

  // 从localStorage获取缓存的状态
  private getCachedStatus(): ServerStatus | null {
    try {
      const cached = localStorage.getItem(SERVER_STATUS_KEY);
      if (cached) {
        const parsedCache: CachedServerStatus = JSON.parse(cached);
        const now = Date.now();
        
        // 检查缓存是否过期
        if (now - parsedCache.timestamp < CACHE_DURATION) {
          return parsedCache.data;
        } else {
          // 清除过期缓存
          localStorage.removeItem(SERVER_STATUS_KEY);
        }
      }
    } catch (error) {
      console.error('Failed to parse cached server status:', error);
      localStorage.removeItem(SERVER_STATUS_KEY);
    }
    return null;
  }

  // 缓存状态到localStorage
  private cacheStatus(serverStatus: ServerStatus) {
    try {
      const cacheData: CachedServerStatus = {
        data: serverStatus,
        timestamp: Date.now(),
      };
      localStorage.setItem(SERVER_STATUS_KEY, JSON.stringify(cacheData));
      
      // 同时存储邀请码状态（兼容旧代码）
      localStorage.setItem(FORCE_INVITE_KEY, serverStatus.invite.toString());
    } catch (error) {
      console.error('Failed to cache server status:', error);
    }
  }

  // 获取服务器状态
  async fetchStatus(forceRefresh = false): Promise<ServerStatus> {
    // 如果已经有正在进行的请求，返回同一个Promise
    if (this.fetchPromise && !forceRefresh) {
      return this.fetchPromise;
    }

    // 如果不是强制刷新，先尝试从缓存获取
    if (!forceRefresh && !this.loading) {
      const cachedStatus = this.getCachedStatus();
      if (cachedStatus) {
        this.status = cachedStatus;
        this.loading = false;
        this.error = null;
        this.notifyListeners();
        return cachedStatus;
      }
    }

    // 创建新的请求Promise
    this.fetchPromise = this.performFetch();
    
    try {
      const result = await this.fetchPromise;
      return result;
    } finally {
      this.fetchPromise = null;
    }
  }

  private async performFetch(): Promise<ServerStatus> {
    try {
      this.loading = true;
      this.error = null;
      this.notifyListeners();
      
      console.log('ServerStatusManager: Fetching server status from API...');
      
      // 从API获取最新状态
      const serverStatus = await gameService.getServerStatus();
      this.status = serverStatus;
      
      // 缓存到localStorage
      this.cacheStatus(serverStatus);
      
      return serverStatus;
    } catch (err) {
      const errorMessage = '获取服务器状态失败';
      this.error = errorMessage;
      console.error('Failed to fetch server status:', err);
      throw err;
    } finally {
      this.loading = false;
      this.notifyListeners();
    }
  }

  // 开始定时刷新
  startAutoRefresh(intervalMs = 30000) {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }
    
    this.refreshTimer = setInterval(() => {
      console.log('ServerStatusManager: Auto-refreshing server status...');
      this.fetchStatus(true);
    }, intervalMs);
  }

  // 停止定时刷新
  stopAutoRefresh() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  // 检查是否启用邀请码注册
  isInviteRequired(): boolean {
    if (this.status) {
      return this.status.invite;
    }
    
    // 从localStorage获取缓存的邀请码状态
    const forceInvite = localStorage.getItem(FORCE_INVITE_KEY);
    return forceInvite === 'true';
  }

  // 获取当前状态
  getCurrentStatus() {
    return {
      status: this.status,
      loading: this.loading,
      error: this.error,
    };
  }
}

export const serverStatusManager = ServerStatusManager.getInstance();
