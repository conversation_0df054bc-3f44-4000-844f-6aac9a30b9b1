/**
 * 测试重复请求修复
 * 验证多个组件同时使用useServerStatus时不会产生重复的API调用
 */

import { serverStatusManager } from '../serverStatusManager';

// Mock gameService
const mockFetch = jest.fn();
jest.mock('../../api/services/game', () => ({
  gameService: {
    getServerStatus: mockFetch,
  },
}));

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('Duplicate Request Fix', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
    mockFetch.mockClear();
  });

  afterEach(() => {
    serverStatusManager.stopAutoRefresh();
  });

  test('should prevent duplicate API calls when multiple components request status simultaneously', async () => {
    const mockServerStatus = {
      isOnline: true,
      status: '正常',
      onlinePlayers: 100,
      count: 100,
      invite: true,
      expRate: 2,
      mesoRate: 2,
      dropRate: 1,
      bossRate: 1,
    };

    mockFetch.mockResolvedValue(mockServerStatus);

    // 模拟多个组件（首页的ServerStatus + 下载页面的useServerStatus）同时请求
    const promise1 = serverStatusManager.fetchStatus();
    const promise2 = serverStatusManager.fetchStatus();
    const promise3 = serverStatusManager.fetchStatus();

    // 等待所有请求完成
    const results = await Promise.all([promise1, promise2, promise3]);

    // 验证：
    // 1. 只调用了一次API
    expect(mockFetch).toHaveBeenCalledTimes(1);
    
    // 2. 所有请求都返回相同的结果
    expect(results[0]).toEqual(mockServerStatus);
    expect(results[1]).toEqual(mockServerStatus);
    expect(results[2]).toEqual(mockServerStatus);
    
    // 3. 结果都是同一个对象引用（共享Promise的结果）
    expect(results[0]).toBe(results[1]);
    expect(results[1]).toBe(results[2]);
  });

  test('should use cached data for subsequent requests within cache duration', async () => {
    const mockServerStatus = {
      isOnline: true,
      status: '正常',
      onlinePlayers: 100,
      count: 100,
      invite: true,
      expRate: 2,
      mesoRate: 2,
      dropRate: 1,
      bossRate: 1,
    };

    // 设置缓存数据
    const cachedData = {
      data: mockServerStatus,
      timestamp: Date.now() - 30000, // 30秒前，仍在缓存期内
    };
    localStorageMock.getItem.mockReturnValue(JSON.stringify(cachedData));

    // 多次请求
    await serverStatusManager.fetchStatus();
    await serverStatusManager.fetchStatus();
    await serverStatusManager.fetchStatus();

    // 应该没有调用API，全部使用缓存
    expect(mockFetch).not.toHaveBeenCalled();
  });

  test('should make new API call when cache is expired', async () => {
    const mockServerStatus = {
      isOnline: true,
      status: '正常',
      onlinePlayers: 100,
      count: 100,
      invite: true,
      expRate: 2,
      mesoRate: 2,
      dropRate: 1,
      bossRate: 1,
    };

    // 设置过期的缓存数据
    const expiredCachedData = {
      data: mockServerStatus,
      timestamp: Date.now() - 70000, // 70秒前，已过期
    };
    localStorageMock.getItem.mockReturnValue(JSON.stringify(expiredCachedData));
    mockFetch.mockResolvedValue(mockServerStatus);

    await serverStatusManager.fetchStatus();

    // 应该调用API获取新数据
    expect(mockFetch).toHaveBeenCalledTimes(1);
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('ServerStatus');
  });
});
