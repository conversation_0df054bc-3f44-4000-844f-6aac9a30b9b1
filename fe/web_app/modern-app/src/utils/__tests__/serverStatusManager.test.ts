import { serverStatusManager } from '../serverStatusManager';
import { gameService } from '../../api/services/game';

// Mock gameService
jest.mock('../../api/services/game');
const mockGameService = gameService as jest.Mocked<typeof gameService>;

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock console.log to track API calls
const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

describe('ServerStatusManager', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
    consoleSpy.mockClear();
  });

  afterEach(() => {
    serverStatusManager.stopAutoRefresh();
  });

  test('should prevent duplicate API calls when multiple components request status', async () => {
    const mockServerStatus = {
      isOnline: true,
      status: '正常',
      onlinePlayers: 100,
      count: 100,
      invite: true,
      expRate: 2,
      mesoRate: 2,
      dropRate: 1,
      bossRate: 1,
    };

    mockGameService.getServerStatus.mockResolvedValue(mockServerStatus);

    // 模拟多个组件同时请求状态
    const promises = [
      serverStatusManager.fetchStatus(),
      serverStatusManager.fetchStatus(),
      serverStatusManager.fetchStatus(),
    ];

    await Promise.all(promises);

    // 应该只调用一次API
    expect(mockGameService.getServerStatus).toHaveBeenCalledTimes(1);
    expect(consoleSpy).toHaveBeenCalledWith('ServerStatusManager: Fetching server status from API...');
  });

  test('should use cached data when available', async () => {
    const cachedData = {
      data: {
        isOnline: true,
        status: '正常',
        onlinePlayers: 50,
        count: 50,
        invite: false,
        expRate: 1,
        mesoRate: 1,
        dropRate: 1,
        bossRate: 1,
      },
      timestamp: Date.now() - 30000, // 30 seconds ago
    };

    localStorageMock.getItem.mockReturnValue(JSON.stringify(cachedData));

    const result = await serverStatusManager.fetchStatus();

    expect(result).toEqual(cachedData.data);
    expect(mockGameService.getServerStatus).not.toHaveBeenCalled();
  });

  test('should notify all listeners when status changes', async () => {
    const mockServerStatus = {
      isOnline: true,
      status: '正常',
      onlinePlayers: 100,
      count: 100,
      invite: true,
      expRate: 2,
      mesoRate: 2,
      dropRate: 1,
      bossRate: 1,
    };

    mockGameService.getServerStatus.mockResolvedValue(mockServerStatus);

    const listener1 = jest.fn();
    const listener2 = jest.fn();

    const unsubscribe1 = serverStatusManager.addListener(listener1);
    const unsubscribe2 = serverStatusManager.addListener(listener2);

    await serverStatusManager.fetchStatus();

    // 每个监听器应该被调用（初始状态 + 加载状态 + 完成状态）
    expect(listener1).toHaveBeenCalledTimes(3);
    expect(listener2).toHaveBeenCalledTimes(3);

    // 最后一次调用应该包含正确的状态
    expect(listener1).toHaveBeenLastCalledWith(mockServerStatus, false, null);
    expect(listener2).toHaveBeenLastCalledWith(mockServerStatus, false, null);

    unsubscribe1();
    unsubscribe2();
  });

  test('should handle auto-refresh correctly', (done) => {
    const mockServerStatus = {
      isOnline: true,
      status: '正常',
      onlinePlayers: 100,
      count: 100,
      invite: true,
      expRate: 2,
      mesoRate: 2,
      dropRate: 1,
      bossRate: 1,
    };

    mockGameService.getServerStatus.mockResolvedValue(mockServerStatus);

    // 启动自动刷新，间隔100ms用于测试
    serverStatusManager.startAutoRefresh(100);

    setTimeout(() => {
      // 应该至少调用了2次（初始 + 至少一次自动刷新）
      expect(mockGameService.getServerStatus).toHaveBeenCalledTimes(2);
      expect(consoleSpy).toHaveBeenCalledWith('ServerStatusManager: Auto-refreshing server status...');
      
      serverStatusManager.stopAutoRefresh();
      done();
    }, 250);
  });
});
