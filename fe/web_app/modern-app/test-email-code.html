<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试邮箱验证码功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试修复后的功能</h1>
        <p>这个页面用于测试修复后的邮箱验证码发送功能和TOS API。</p>
        
        <div class="form-group">
            <label for="email">邮箱地址:</label>
            <input type="email" id="email" placeholder="请输入邮箱地址" value="<EMAIL>">
        </div>
        
        <button id="sendCodeBtn" onclick="sendEmailCode()">发送验证码</button>
        <button id="testTosBtn" onclick="testTosApi()" style="margin-left: 10px;">测试TOS API</button>

        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        async function sendEmailCode() {
            const email = document.getElementById('email').value;
            const resultDiv = document.getElementById('result');
            const sendBtn = document.getElementById('sendCodeBtn');
            
            if (!email) {
                showResult('请输入邮箱地址', 'error');
                return;
            }
            
            // 禁用按钮
            sendBtn.disabled = true;
            sendBtn.textContent = '发送中...';
            
            try {
                // 调用API
                const response = await fetch('/register/captcha', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email: email })
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 0) {
                    showResult(`验证码发送成功！\n响应数据: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult(`发送失败: ${data.message || '未知错误'}\n完整响应: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult(`网络错误: ${error.message}`, 'error');
            } finally {
                // 重新启用按钮
                sendBtn.disabled = false;
                sendBtn.textContent = '发送验证码';
            }
        }
        
        async function testTosApi() {
            const resultDiv = document.getElementById('result');
            const testBtn = document.getElementById('testTosBtn');

            // 禁用按钮
            testBtn.disabled = true;
            testBtn.textContent = '测试中...';

            try {
                // 调用TOS API
                const response = await fetch('/api/tos', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();

                if (response.ok && data.code === 0) {
                    showResult(`TOS API测试成功！\n内容长度: ${data.data.content ? data.data.content.length : 0} 字符\n响应数据: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult(`TOS API测试失败: ${data.message || '未知错误'}\n完整响应: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult(`TOS API网络错误: ${error.message}`, 'error');
            } finally {
                // 重新启用按钮
                testBtn.disabled = false;
                testBtn.textContent = '测试TOS API';
            }
        }

        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }
    </script>
</body>
</html>
