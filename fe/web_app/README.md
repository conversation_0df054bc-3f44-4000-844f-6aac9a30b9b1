# MapleStory Web Application

一个MapleStory私人服务器（MagicMS）的web应用，它提供注册、签到、公告、商城、资料库等社区功能。

# 项目重构

这个应用已经被完整的实现在了`demo`，出于历史原因，早期的实现使用了一些“非现代化”的工具和代码，本项目的目标是使用现代化前端技术重头构建一个web应用。


## 主要技术栈

- React 19 (客户端渲染、ULR路由)
- TypeScript
- Tailwind CSS
- Shadcn UI

## 核心功能

- [ ] i18n国际化支持
- [ ] 主页
- [ ] 下载页
- [ ] 排行页
- [ ] 投票页
- [ ] 公告页
- [ ] 商城页
- [ ] 资料库

其中，排行榜、商城页和资料库是重点重构功能，我们需要使用可复用的组件化方式来替代早期的html字符串填充，增加代码复用程度和可维护性。

## 重构进度

在这里记录项目实施进度